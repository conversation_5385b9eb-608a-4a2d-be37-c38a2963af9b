{% extends "student/base.html" %}

{% block title %}{{ translations.request_summary }}{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>{{ translations.request_summary }}</h1>
    <p>{{ translations.review_request_details }}</p>
</div>

<div class="card">
    <div class="card-header">
        <h2>{{ translations.request_details }}</h2>
    </div>
    <div class="card-body">
        <div class="summary-item">
            <div class="summary-label">{{ translations.academic_years }}:</div>
            <div class="summary-value">{{ request.academic_years|join(', ') }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">{{ translations.number_of_transcripts }}:</div>
            <div class="summary-value">{{ request.count }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">{{ translations.price_per_transcript }}:</div>
            <div class="summary-value">1,000 RWF</div>
        </div>
        <div class="summary-item total-row">
            <div class="summary-label">{{ translations.total_amount }}:</div>
            <div class="summary-value">{{ request.total_price }} RWF</div>
        </div>

        <div class="form-group" style="margin-top: 30px;">
            <a href="{{ url_for('student.payment') }}" class="btn btn-primary btn-block">
                <i class="fas fa-credit-card"></i> {{ translations.proceed_to_payment }}
            </a>
        </div>

        <div class="form-group">
            <a href="{{ url_for('student.request_transcript') }}" class="btn btn-secondary btn-block">
                <i class="fas fa-arrow-left"></i> {{ translations.back_to_request }}
            </a>
        </div>
    </div>
    <div class="card-footer">
        <p><i class="fas fa-info-circle"></i> {{ translations.payment_redirect_note }}</p>
    </div>
</div>
{% endblock %}
