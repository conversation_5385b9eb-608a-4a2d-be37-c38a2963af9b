{% extends "base.html" %}

{% block title %}Upload Payment Proof - INES Transcript System{% endblock %}

{% block content %}
<div class="container">
    <!-- Header Section -->
    <div class="payment-proof-header">
        <div class="header-content">
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Request Details</div>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <div class="step-label">Payment Method</div>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-label">Payment Proof</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Submission</div>
                </div>
            </div>
            
            <h1>Upload Payment Proof</h1>
            <p class="subtitle">Please upload a screenshot of your payment transaction to complete your request</p>
        </div>
    </div>

    <!-- Payment Summary Card -->
    <div class="payment-summary-card">
        <h3><i class="fas fa-receipt"></i> Payment Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="label">Academic Years:</span>
                <span class="value">{{ session.transcript_request.academic_years|join(', ') }}</span>
            </div>
            <div class="summary-item">
                <span class="label">Total Amount:</span>
                <span class="value amount">{{ "{:,.0f}".format(session.transcript_request.total_price) }} RWF</span>
            </div>
            <div class="summary-item">
                <span class="label">Payment Method:</span>
                <span class="value method">{{ session.transcript_request.payment_method|title }}</span>
            </div>
        </div>
    </div>

    <!-- Upload Instructions -->
    <div class="upload-instructions">
        <div class="instruction-card">
            <div class="instruction-header">
                <i class="fas fa-cloud-upload-alt payment-icon"></i>
                <h4>Upload Payment Proof</h4>
            </div>
            <p style="color: #666; margin-bottom: 0;">Please upload a clear screenshot or photo of your payment confirmation to complete your transcript request.</p>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="upload-form-container">
        <form method="POST" action="{{ url_for('submit_payment_proof') }}" enctype="multipart/form-data" class="upload-form">
            <div class="upload-section">
                <h3><i class="fas fa-cloud-upload-alt"></i> Upload Payment Proof</h3>
                
                <div class="file-upload-area" id="fileUploadArea">
                    <div class="upload-content">
                        <i class="fas fa-image upload-icon"></i>
                        <h4>Drag & Drop your screenshot here</h4>
                        <p>or click to browse files</p>
                        <input type="file" name="payment_proof" id="paymentProof" accept="image/*" required>
                    </div>
                    <div class="file-preview" id="filePreview" style="display: none;">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="file-info">
                            <span id="fileName"></span>
                            <button type="button" id="removeFile" class="remove-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="upload-requirements">
                    <h4><i class="fas fa-check-circle"></i> Requirements:</h4>
                    <ul>
                        <li>Image formats: JPG, PNG, or PDF</li>
                        <li>Maximum file size: 5MB</li>
                        <li>Screenshot must be clear and readable</li>
                        <li>Must show transaction amount and confirmation</li>
                    </ul>
                </div>
            </div>

            <div class="form-actions">
                <a href="{{ url_for('payment') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payment
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    <i class="fas fa-paper-plane"></i> Submit Request
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="help-section">
        <h3><i class="fas fa-question-circle"></i> Need Help?</h3>
        <div class="help-grid">
            <div class="help-item">
                <i class="fas fa-phone"></i>
                <div>
                    <strong>Call Finance Office</strong>
                    <p>+250 788 123 456</p>
                </div>
            </div>
            <div class="help-item">
                <i class="fas fa-envelope"></i>
                <div>
                    <strong>Email Support</strong>
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="help-item">
                <i class="fas fa-clock"></i>
                <div>
                    <strong>Office Hours</strong>
                    <p>Mon-Fri: 8:00 AM - 5:00 PM</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Submission Feedback Modal -->
<div class="submission-overlay" id="submissionOverlay">
    <div class="submission-modal">
        <div class="submission-icon" id="submissionIcon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h2 class="submission-title" id="submissionTitle">Request Submitted!</h2>
        <p class="submission-message" id="submissionMessage">
            Your transcript request has been submitted successfully and is being processed by the finance office.
        </p>
        <div class="loading-spinner" id="loadingSpinner"></div>
        <button class="submission-btn" id="submissionBtn" onclick="closeSubmissionModal()">
            Continue to Dashboard
        </button>
    </div>
</div>

<style>
/* Payment Proof Page Styles */
.payment-proof-header {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    padding: 40px 0;
    margin: -20px -20px 30px -20px;
    border-radius: 0 0 20px 20px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.completed .step-number {
    background: #4CAF50;
}

.step.active .step-number {
    background: #FF9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.5);
}

.step-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.payment-proof-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.payment-summary-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    border-left: 5px solid #083464;
}

.payment-summary-card h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.summary-item .label {
    font-weight: 500;
    color: #666;
}

.summary-item .value {
    font-weight: 700;
    color: #083464;
}

.summary-item .value.amount {
    color: #4CAF50;
    font-size: 1.1rem;
}

.upload-instructions {
    margin-bottom: 30px;
}

.instruction-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.instruction-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.payment-icon {
    font-size: 3rem;
    color: #083464;
}

.instruction-header h4 {
    color: #083464;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

/* Submission feedback styles */
.submission-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.submission-modal {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.submission-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.submission-icon.success {
    color: #4CAF50;
}

.submission-icon.error {
    color: #F44336;
}

.submission-title {
    color: #083464;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.submission-message {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.submission-btn {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submission-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(8, 52, 100, 0.3);
}

.loading-spinner {
    display: none;
    margin: 20px auto;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #083464;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.upload-form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.upload-section h3 {
    color: #083464;
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.file-upload-area {
    border: 3px dashed #083464;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin-bottom: 25px;
}

.file-upload-area:hover {
    border-color: #1976D2;
    background: rgba(8, 52, 100, 0.02);
}

.file-upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: #083464;
    margin-bottom: 15px;
}

.upload-content h4 {
    color: #083464;
    margin-bottom: 10px;
}

.upload-content p {
    color: #666;
    margin: 0;
}

#paymentProof {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.file-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 10px;
}

.file-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.upload-requirements {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4CAF50;
}

.upload-requirements h4 {
    color: #2e7d32;
    margin-bottom: 15px;
}

.upload-requirements ul {
    margin: 0;
    padding-left: 20px;
}

.upload-requirements li {
    margin-bottom: 8px;
    color: #2e7d32;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.btn {
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(8, 52, 100, 0.3);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.help-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.help-section h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.help-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.help-item i {
    font-size: 1.5rem;
    color: #083464;
}

.help-item strong {
    color: #083464;
    display: block;
    margin-bottom: 5px;
}

.help-item p {
    margin: 0;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .step-indicator {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .help-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('paymentProof');
    const uploadArea = document.getElementById('fileUploadArea');
    const filePreview = document.getElementById('filePreview');
    const uploadContent = uploadArea.querySelector('.upload-content');
    const previewImage = document.getElementById('previewImage');
    const fileName = document.getElementById('fileName');
    const removeBtn = document.getElementById('removeFile');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.querySelector('.upload-form');

    // File upload handling
    uploadArea.addEventListener('click', () => fileInput.click());
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });
    
    removeBtn.addEventListener('click', () => {
        fileInput.value = '';
        uploadContent.style.display = 'block';
        filePreview.style.display = 'none';
        submitBtn.disabled = true;
    });

    // Handle form submission with feedback
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files.length) {
            showSubmissionFeedback(false, 'No File Selected', 'Please select a payment proof file before submitting.');
            return;
        }

        // Show loading state
        showLoadingState();

        // Create FormData and submit
        const formData = new FormData(uploadForm);

        fetch(uploadForm.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            // Check if response contains success indicators
            if (data.includes('success') || data.includes('submitted')) {
                showSubmissionFeedback(true, 'Request Submitted Successfully!',
                    'Your transcript request with payment proof has been submitted to the finance office for review. You will receive email updates on the status.');
            } else {
                showSubmissionFeedback(false, 'Submission Error',
                    'There was an error submitting your request. Please try again or contact support.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSubmissionFeedback(false, 'Submission Failed',
                'Failed to submit your request. Please check your internet connection and try again.');
        });
    });

    function handleFile(file) {
        // Validate file type
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!validTypes.includes(file.type)) {
            alert('Please upload a valid image file (JPG, PNG) or PDF.');
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }
        
        // Show preview
        fileName.textContent = file.name;
        
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            previewImage.style.display = 'none';
        }
        
        uploadContent.style.display = 'none';
        filePreview.style.display = 'flex';
        submitBtn.disabled = false;
    }

    // Submission feedback functions
    function showLoadingState() {
        const overlay = document.getElementById('submissionOverlay');
        const icon = document.getElementById('submissionIcon');
        const title = document.getElementById('submissionTitle');
        const message = document.getElementById('submissionMessage');
        const spinner = document.getElementById('loadingSpinner');
        const btn = document.getElementById('submissionBtn');

        icon.innerHTML = '<i class="fas fa-upload"></i>';
        icon.className = 'submission-icon';
        title.textContent = 'Submitting Request...';
        message.textContent = 'Please wait while we process your payment proof and submit your request.';
        spinner.style.display = 'block';
        btn.style.display = 'none';

        overlay.style.display = 'flex';
    }

    function showSubmissionFeedback(success, title, message) {
        const overlay = document.getElementById('submissionOverlay');
        const icon = document.getElementById('submissionIcon');
        const titleEl = document.getElementById('submissionTitle');
        const messageEl = document.getElementById('submissionMessage');
        const spinner = document.getElementById('loadingSpinner');
        const btn = document.getElementById('submissionBtn');

        spinner.style.display = 'none';
        btn.style.display = 'block';

        if (success) {
            icon.innerHTML = '<i class="fas fa-check-circle"></i>';
            icon.className = 'submission-icon success';
            btn.textContent = 'Go to Dashboard';
        } else {
            icon.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
            icon.className = 'submission-icon error';
            btn.textContent = 'Try Again';
        }

        titleEl.textContent = title;
        messageEl.textContent = message;
        overlay.style.display = 'flex';
    }
});

// Close submission modal function
function closeSubmissionModal() {
    const overlay = document.getElementById('submissionOverlay');
    overlay.style.display = 'none';

    // Redirect to dashboard
    window.location.href = '/student/dashboard';
}
</script>
{% endblock %}
