"""
Mobile Money (MoMo) API Integration Service
Handles payment processing with Intouch Mobile Money API
"""

import requests
import uuid
import os
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntouchMoMoAPI:
    def __init__(self):
        """
        Initialize Intouch MoMo API client with real credentials
        """
        # Real Intouch API credentials
        self.base_url = 'https://www.intouchpay.co.rw/api'
        self.username = 'testa'
        self.account_no = '************'
        self.password = '+$J<wtZktTDs&-Mk("h5=<PH#Jf769P5/Z<*xbR~'

        self.access_token = None
        self.token_expires_at = None

        # Test mode for when API is down
        self.test_mode = os.getenv('MOMO_TEST_MODE', 'false').lower() == 'true'
    
    def authenticate(self):
        """Authenticate with Intouch API"""
        url = f"{self.base_url}/login"

        payload = {
            'username': self.username,
            'password': self.password,
            'account_no': self.account_no
        }

        headers = {
            'Content-Type': 'application/json'
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()

            data = response.json()
            if data.get('success'):
                self.access_token = data.get('token')
                # Token expires in 1 hour, refresh 5 minutes early
                self.token_expires_at = datetime.now() + timedelta(hours=1, minutes=-5)
                logger.info("Intouch API authentication successful")
                return self.access_token
            else:
                raise Exception(f"Authentication failed: {data.get('message', 'Unknown error')}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to authenticate with Intouch API: {e}")
            raise Exception(f"Failed to authenticate with Intouch API: {e}")

    def get_access_token(self):
        """Get or refresh access token"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token

        return self.authenticate()
    
    def request_to_pay(self, phone_number, amount, currency='RWF', external_id=None, payer_message=None):
        """
        Request payment from a customer using Intouch API

        Args:
            phone_number: Customer's phone number (format: 250788123456)
            amount: Amount to charge
            currency: Currency code (default: RWF)
            external_id: External reference ID
            payer_message: Message to show to payer
            payee_note: Note for payee

        Returns:
            dict: Transaction details including transaction_id
        """
        # Test mode fallback when API is down
        if self.test_mode:
            logger.info("Using test mode for payment request")
            transaction_id = external_id or str(uuid.uuid4())
            return {
                'success': True,
                'transaction_id': transaction_id,
                'status': 'pending',
                'message': 'Payment request sent to customer (TEST MODE)',
                'phone_number': phone_number,
                'amount': amount,
                'currency': currency,
                'reference': f'TEST_{transaction_id[:8]}'
            }

        try:
            access_token = self.get_access_token()
        except Exception as e:
            logger.error(f"Failed to get access token for payment: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Payment service temporarily unavailable. Please try again later.'
            }

        transaction_id = external_id or str(uuid.uuid4())

        url = f"{self.base_url}/requestpayment"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        # Ensure phone number is in correct format for Intouch
        if phone_number.startswith('+'):
            phone_number = phone_number[1:]
        elif phone_number.startswith('0'):
            phone_number = '250' + phone_number[1:]

        payload = {
            'account_no': self.account_no,
            'phone_number': phone_number,
            'amount': int(amount),
            'currency': currency,
            'transaction_id': transaction_id,
            'description': payer_message or f'INES Transcript Payment - {amount} {currency}',
            'callback_url': 'https://your-domain.com/payment-callback'  # Optional callback URL
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()

            data = response.json()

            if data.get('success'):
                logger.info(f"Payment request initiated successfully. Transaction ID: {transaction_id}")

                return {
                    'success': True,
                    'transaction_id': transaction_id,
                    'status': 'pending',
                    'message': 'Payment request sent to customer',
                    'phone_number': phone_number,
                    'amount': amount,
                    'currency': currency,
                    'reference': data.get('reference')
                }
            else:
                logger.error(f"Payment request failed: {data.get('message', 'Unknown error')}")
                return {
                    'success': False,
                    'error': data.get('message', 'Unknown error'),
                    'message': 'Failed to initiate payment request'
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to request payment: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to initiate payment request'
            }
    
    def check_payment_status(self, transaction_id):
        """
        Check the status of a payment transaction using Intouch API

        Args:
            transaction_id: The transaction ID to check

        Returns:
            dict: Payment status information
        """
        # Test mode fallback when API is down
        if self.test_mode:
            logger.info("Using test mode for payment status check")
            # Simulate manual payment completion for USSD payments
            import time
            import random

            # For manual payments, simulate realistic completion timing
            # This gives time for user to complete USSD payment
            if transaction_id.startswith('TEST_'):
                # Simulate random completion time for realistic testing
                completion_chance = random.random()
                if completion_chance > 0.7:  # 30% chance of completion each check
                    return {
                        'success': True,
                        'status': 'completed',
                        'transaction_id': transaction_id,
                        'message': 'Payment completed successfully (Manual USSD Payment Detected)',
                        'amount': 100,
                        'currency': 'RWF',
                        'payment_method': 'USSD *182*7*1#'
                    }
                else:
                    return {
                        'success': True,
                        'status': 'pending',
                        'transaction_id': transaction_id,
                        'message': 'Waiting for manual payment completion...'
                    }

        try:
            access_token = self.get_access_token()
        except Exception as e:
            logger.error(f"Failed to get access token for status check: {e}")
            return {
                'success': False,
                'status': 'unknown',
                'error': str(e),
                'message': 'Payment status service temporarily unavailable'
            }

        url = f"{self.base_url}/checkstatus"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        payload = {
            'transaction_id': transaction_id,
            'account_no': self.account_no
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()

            data = response.json()

            if data.get('success'):
                status = data.get('status', 'unknown').lower()

                # Map Intouch status to our standard status
                if status in ['completed', 'success', 'successful']:
                    mapped_status = 'successful'
                elif status in ['failed', 'error', 'cancelled']:
                    mapped_status = 'failed'
                else:
                    mapped_status = 'pending'

                logger.info(f"Payment status for {transaction_id}: {mapped_status}")

                return {
                    'success': True,
                    'transaction_id': transaction_id,
                    'status': mapped_status,  # 'pending', 'successful', 'failed'
                    'amount': data.get('amount'),
                    'currency': data.get('currency'),
                    'reference': data.get('reference'),
                    'phone_number': data.get('phone_number'),
                    'message': data.get('message', ''),
                    'raw_status': status  # Original status from Intouch
                }
            else:
                logger.error(f"Failed to check payment status: {data.get('message', 'Unknown error')}")
                return {
                    'success': False,
                    'error': data.get('message', 'Unknown error'),
                    'message': 'Failed to check payment status'
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to check payment status: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to check payment status'
            }
    
    def get_account_balance(self):
        """Get account balance using Intouch API"""
        access_token = self.get_access_token()

        url = f"{self.base_url}/balance"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        payload = {
            'account_no': self.account_no
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()

            data = response.json()

            if data.get('success'):
                logger.info("Account balance retrieved successfully")

                return {
                    'success': True,
                    'available_balance': data.get('balance'),
                    'currency': data.get('currency', 'RWF')
                }
            else:
                logger.error(f"Failed to get account balance: {data.get('message', 'Unknown error')}")
                return {
                    'success': False,
                    'error': data.get('message', 'Unknown error'),
                    'message': 'Failed to get account balance'
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get account balance: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to get account balance'
            }

# Global Intouch MoMo API instance
momo_api = IntouchMoMoAPI()

def initiate_payment(phone_number, amount, external_id=None, payer_message=None):
    """
    Convenience function to initiate a payment
    
    Args:
        phone_number: Customer's phone number
        amount: Amount to charge
        external_id: External reference ID
        payer_message: Message to show to payer
        
    Returns:
        dict: Payment initiation result
    """
    return momo_api.request_to_pay(
        phone_number=phone_number,
        amount=amount,
        external_id=external_id,
        payer_message=payer_message
    )

def check_payment(transaction_id):
    """
    Convenience function to check payment status
    
    Args:
        transaction_id: Transaction ID to check
        
    Returns:
        dict: Payment status result
    """
    return momo_api.check_payment_status(transaction_id)

def get_balance():
    """
    Convenience function to get account balance
    
    Returns:
        dict: Account balance information
    """
    return momo_api.get_account_balance()
