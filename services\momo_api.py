"""
Mobile Money (MoMo) API Integration Service
Handles payment processing with MTN Mobile Money API
"""

import requests
import uuid
import json
import time
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MoMoAPI:
    def __init__(self, environment='sandbox'):
        """
        Initialize MoMo API client
        
        Args:
            environment: 'sandbox' or 'production'
        """
        self.environment = environment
        
        if environment == 'sandbox':
            self.base_url = 'https://sandbox.momodeveloper.mtn.com'
            # Sandbox credentials - replace with your actual sandbox credentials
            self.subscription_key = 'your_sandbox_subscription_key'
            self.user_id = 'your_sandbox_user_id'
            self.api_key = 'your_sandbox_api_key'
        else:
            self.base_url = 'https://momodeveloper.mtn.com'
            # Production credentials - replace with your actual production credentials
            self.subscription_key = 'your_production_subscription_key'
            self.user_id = 'your_production_user_id'
            self.api_key = 'your_production_api_key'
        
        self.access_token = None
        self.token_expires_at = None
    
    def get_access_token(self):
        """Get or refresh access token"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token
        
        url = f"{self.base_url}/collection/token/"
        headers = {
            'Authorization': f'Basic {self.api_key}',
            'Ocp-Apim-Subscription-Key': self.subscription_key
        }
        
        try:
            response = requests.post(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            self.access_token = data['access_token']
            # Token expires in 1 hour, refresh 5 minutes early
            self.token_expires_at = datetime.now() + timedelta(seconds=data['expires_in'] - 300)
            
            logger.info("Access token obtained successfully")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get access token: {e}")
            raise Exception(f"Failed to authenticate with MoMo API: {e}")
    
    def request_to_pay(self, phone_number, amount, currency='RWF', external_id=None, payer_message=None, payee_note=None):
        """
        Request payment from a customer
        
        Args:
            phone_number: Customer's phone number (format: ************)
            amount: Amount to charge
            currency: Currency code (default: RWF)
            external_id: External reference ID
            payer_message: Message to show to payer
            payee_note: Note for payee
            
        Returns:
            dict: Transaction details including transaction_id
        """
        access_token = self.get_access_token()
        transaction_id = str(uuid.uuid4())
        
        url = f"{self.base_url}/collection/v1_0/requesttopay"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'X-Reference-Id': transaction_id,
            'X-Target-Environment': self.environment,
            'Ocp-Apim-Subscription-Key': self.subscription_key,
            'Content-Type': 'application/json'
        }
        
        # Ensure phone number is in correct format
        if phone_number.startswith('+'):
            phone_number = phone_number[1:]
        elif phone_number.startswith('0'):
            phone_number = '250' + phone_number[1:]
        
        payload = {
            'amount': str(amount),
            'currency': currency,
            'externalId': external_id or transaction_id,
            'payer': {
                'partyIdType': 'MSISDN',
                'partyId': phone_number
            },
            'payerMessage': payer_message or f'Payment for transcript request - {amount} {currency}',
            'payeeNote': payee_note or 'INES Transcript System Payment'
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            logger.info(f"Payment request initiated successfully. Transaction ID: {transaction_id}")
            
            return {
                'success': True,
                'transaction_id': transaction_id,
                'status': 'pending',
                'message': 'Payment request sent to customer',
                'phone_number': phone_number,
                'amount': amount,
                'currency': currency
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to request payment: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to initiate payment request'
            }
    
    def check_payment_status(self, transaction_id):
        """
        Check the status of a payment transaction
        
        Args:
            transaction_id: The transaction ID to check
            
        Returns:
            dict: Payment status information
        """
        access_token = self.get_access_token()
        
        url = f"{self.base_url}/collection/v1_0/requesttopay/{transaction_id}"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'X-Target-Environment': self.environment,
            'Ocp-Apim-Subscription-Key': self.subscription_key
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            status = data.get('status', 'unknown').lower()
            
            logger.info(f"Payment status for {transaction_id}: {status}")
            
            return {
                'success': True,
                'transaction_id': transaction_id,
                'status': status,  # 'pending', 'successful', 'failed'
                'amount': data.get('amount'),
                'currency': data.get('currency'),
                'financial_transaction_id': data.get('financialTransactionId'),
                'external_id': data.get('externalId'),
                'payer': data.get('payer', {}),
                'reason': data.get('reason', {})
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to check payment status: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to check payment status'
            }
    
    def get_account_balance(self):
        """Get account balance"""
        access_token = self.get_access_token()
        
        url = f"{self.base_url}/collection/v1_0/account/balance"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'X-Target-Environment': self.environment,
            'Ocp-Apim-Subscription-Key': self.subscription_key
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            logger.info("Account balance retrieved successfully")
            
            return {
                'success': True,
                'available_balance': data.get('availableBalance'),
                'currency': data.get('currency')
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get account balance: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to get account balance'
            }

# Global MoMo API instance
momo_api = MoMoAPI(environment='sandbox')  # Change to 'production' for live environment

def initiate_payment(phone_number, amount, external_id=None, payer_message=None):
    """
    Convenience function to initiate a payment
    
    Args:
        phone_number: Customer's phone number
        amount: Amount to charge
        external_id: External reference ID
        payer_message: Message to show to payer
        
    Returns:
        dict: Payment initiation result
    """
    return momo_api.request_to_pay(
        phone_number=phone_number,
        amount=amount,
        external_id=external_id,
        payer_message=payer_message
    )

def check_payment(transaction_id):
    """
    Convenience function to check payment status
    
    Args:
        transaction_id: Transaction ID to check
        
    Returns:
        dict: Payment status result
    """
    return momo_api.check_payment_status(transaction_id)

def get_balance():
    """
    Convenience function to get account balance
    
    Returns:
        dict: Account balance information
    """
    return momo_api.get_account_balance()
