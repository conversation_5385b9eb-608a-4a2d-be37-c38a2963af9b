#!/usr/bin/env python3
"""
Test script to check Intouch API status
"""

import requests
import json

def test_intouch_api():
    """Test if Intouch API is working"""
    
    print("🔍 Testing Intouch Mobile Money API...")
    print("=" * 50)
    
    # API credentials
    base_url = "https://www.intouchpay.co.rw/api"
    username = "testa"
    password = '+$J<wtZktTDs&-Mk("h5=<PH#Jf769P5/Z<*xbR~'
    account_no = "************"
    
    # Test 1: Check if the API endpoint is reachable
    print("📡 Test 1: Checking API endpoint reachability...")
    try:
        response = requests.get(f"{base_url}/login", timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 502:
            print("   ❌ API is returning 502 Bad Gateway - Server is down")
            return False
        elif response.status_code == 405:
            print("   ✅ API endpoint is reachable (405 = Method Not Allowed for GET)")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Connection error: {e}")
        return False
    
    # Test 2: Try authentication
    print("\n🔐 Test 2: Testing authentication...")
    try:
        auth_data = {
            "username": username,
            "password": password,
            "account_no": account_no
        }
        
        response = requests.post(
            f"{base_url}/login",
            json=auth_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Authentication successful!")
            try:
                data = response.json()
                if 'access_token' in data:
                    print(f"   ✅ Access token received: {data['access_token'][:20]}...")
                    return True
                else:
                    print(f"   ⚠️  No access token in response: {data}")
            except json.JSONDecodeError:
                print(f"   ⚠️  Invalid JSON response: {response.text}")
                
        elif response.status_code == 502:
            print("   ❌ 502 Bad Gateway - API server is down")
            return False
        elif response.status_code == 401:
            print("   ❌ 401 Unauthorized - Check credentials")
            return False
        else:
            print(f"   ❌ Authentication failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request error: {e}")
        return False
    
    return False

def main():
    print("🧪 Intouch Mobile Money API Status Checker")
    print("=" * 50)
    
    is_working = test_intouch_api()
    
    print("\n" + "=" * 50)
    if is_working:
        print("✅ RESULT: Intouch API is WORKING")
        print("💡 You can disable test mode to receive real SMS prompts")
        print("💡 Edit app.py and uncomment the automatic detection code")
    else:
        print("❌ RESULT: Intouch API is NOT WORKING")
        print("💡 The API appears to be experiencing server issues")
        print("💡 Test mode will continue to simulate payments")
        print("💡 Try again later when the service is restored")
    
    print("\n📞 For real SMS prompts, the Intouch API must be working")
    print("🔧 Current system is in test mode to prevent crashes")

if __name__ == "__main__":
    main()
