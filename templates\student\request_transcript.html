{% extends "student/base.html" %}

{% block title %}{{ translations.request_transcript }}{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>{{ translations.request_transcript }}</h1>
    <p>{{ translations.request_transcript_desc }}</p>
</div>

<!-- Department Information Card -->
{% if department_info %}
<div class="card mb-4" style="border-left: 4px solid #007bff;">
    <div class="card-body">
        <h5 class="card-title"><i class="fas fa-graduation-cap"></i> {{ department_info.department }} Program Information</h5>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Program Duration:</strong> {{ department_info.program_duration }} years</p>
                <p><strong>Program Type:</strong> {{ department_info.program_type }}</p>
            </div>
            <div class="col-md-6">
                {% if current_academic_year %}
                <p><strong>Current Academic Year:</strong> {{ current_academic_year }}</p>
                <p class="text-info"><i class="fas fa-info-circle"></i> You cannot request transcripts for your current academic year until it is completed.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="card">
    <div class="card-header">
        <h2>{{ translations.request_transcript }}</h2>
    </div>
    <div class="card-body">
        <form action="{{ url_for('request_transcript') }}" method="POST">
            <div class="form-group">
                <label>{{ translations.academic_years }}</label>
                <p class="form-hint">Select the completed academic years for which you need transcripts. Only completed years are available for selection.</p>

                {% if available_years %}
                <div class="checkbox-group">
                    {% for year in available_years %}
                        <div class="checkbox-item">
                            <input type="checkbox" id="year-{{ loop.index }}" name="academic_years" value="{{ year }}">
                            <label for="year-{{ loop.index }}">{{ year }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>No Academic Years Available</strong><br>
                    {% if department_info %}
                    You have no completed academic years available for transcript request in your {{ department_info.department }} program.
                    {% else %}
                    No completed academic years found for your program.
                    {% endif %}
                    <br><br>
                    <strong>Possible reasons:</strong>
                    <ul>
                        <li>You are currently in your first year</li>
                        <li>You haven't completed any full academic years yet</li>
                        <li>Your enrollment records need to be updated</li>
                    </ul>
                    Please contact the Administration Office if you believe this is an error.
                </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="email">{{ translations.email }}</label>
                <div class="input-group">
                    <span class="input-icon"><i class="fas fa-envelope"></i></span>
                    <input type="email" id="email" name="email" value="{{ session.email }}" required>
                </div>
                <p class="form-hint">{{ translations.request_transcript_desc }}</p>
            </div>

            <div class="form-group">
                {% if available_years %}
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-paper-plane"></i> {{ translations.submit }}
                </button>
                {% else %}
                <button type="button" class="btn btn-secondary" disabled>
                    <i class="fas fa-ban"></i> No Years Available
                </button>
                <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-primary ml-2">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                {% endif %}
            </div>

            <!-- Debug information area -->
            <div id="debugInfo" class="mt-3" style="display: none;">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Form Debug Information:</h6>
                    <div id="debugContent"></div>
                </div>
            </div>
        </form>
    </div>
    <div class="card-footer">
        <p><i class="fas fa-info-circle"></i> {{ translations.transcript_cost_note }}</p>
    </div>
</div>

<script>
    // Simple form validation and submission handling
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        let isSubmitting = false;

        if (form) {
            form.addEventListener('submit', function(event) {
                // Prevent duplicate submissions
                if (isSubmitting) {
                    event.preventDefault();
                    return false;
                }

                // Simple validation - check if at least one academic year is selected
                const checkedBoxes = document.querySelectorAll('input[name="academic_years"]:checked');

                if (checkedBoxes.length === 0) {
                    alert('Please select at least one academic year.');
                    event.preventDefault();
                    return false;
                }

                // Set submitting flag and show loading state
                isSubmitting = true;
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                }

                return true;
            });

        }
    });
</script>
{% endblock %}
