{% extends "student/base.html" %}

{% block title %}{{ translations.request_transcript }}{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>{{ translations.request_transcript }}</h1>
    <p>{{ translations.request_transcript_desc }}</p>
</div>

<!-- Department Information Card -->
{% if department_info %}
<div class="card mb-4" style="border-left: 4px solid #007bff;">
    <div class="card-body">
        <h5 class="card-title"><i class="fas fa-graduation-cap"></i> {{ department_info.department }} Program Information</h5>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Program Duration:</strong> {{ department_info.program_duration }} years</p>
                <p><strong>Program Type:</strong> {{ department_info.program_type }}</p>
            </div>
            <div class="col-md-6">
                {% if current_academic_year %}
                <p><strong>Current Academic Year:</strong> {{ current_academic_year }}</p>
                <p class="text-info"><i class="fas fa-info-circle"></i> You cannot request transcripts for your current academic year until it is completed.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="card">
    <div class="card-header">
        <h2>{{ translations.request_transcript }}</h2>
    </div>
    <div class="card-body">
        <form action="{{ url_for('student.request_transcript') }}" method="POST">
            <div class="form-group">
                <label>{{ translations.academic_years }}</label>
                <p class="form-hint">Select the completed academic years for which you need transcripts. Only completed years are available for selection.</p>

                {% if available_years %}
                <div class="checkbox-group">
                    {% for year in available_years %}
                        <div class="checkbox-item">
                            <input type="checkbox" id="year-{{ loop.index }}" name="academic_years" value="{{ year }}">
                            <label for="year-{{ loop.index }}">{{ year }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>No Academic Years Available</strong><br>
                    {% if department_info %}
                    You have no completed academic years available for transcript request in your {{ department_info.department }} program.
                    {% else %}
                    No completed academic years found for your program.
                    {% endif %}
                    <br><br>
                    <strong>Possible reasons:</strong>
                    <ul>
                        <li>You are currently in your first year</li>
                        <li>You haven't completed any full academic years yet</li>
                        <li>Your enrollment records need to be updated</li>
                    </ul>
                    Please contact the Administration Office if you believe this is an error.
                </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="email">{{ translations.email }}</label>
                <div class="input-group">
                    <span class="input-icon"><i class="fas fa-envelope"></i></span>
                    <input type="email" id="email" name="email" value="{{ session.email }}" required>
                </div>
                <p class="form-hint">{{ translations.request_transcript_desc }}</p>
            </div>

            <div class="form-group">
                {% if available_years %}
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-paper-plane"></i> {{ translations.submit }}
                </button>
                <button type="button" class="btn btn-success ml-2" id="bypassBtn" onclick="bypassValidation()">
                    <i class="fas fa-rocket"></i> Submit Without Validation
                </button>
                <button type="button" class="btn btn-outline-info ml-2" id="debugBtn" onclick="debugForm()">
                    <i class="fas fa-bug"></i> Debug Form
                </button>
                {% else %}
                <button type="button" class="btn btn-secondary" disabled>
                    <i class="fas fa-ban"></i> No Years Available
                </button>
                <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-primary ml-2">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                {% endif %}
            </div>

            <!-- Debug information area -->
            <div id="debugInfo" class="mt-3" style="display: none;">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Form Debug Information:</h6>
                    <div id="debugContent"></div>
                </div>
            </div>
        </form>
    </div>
    <div class="card-footer">
        <p><i class="fas fa-info-circle"></i> {{ translations.transcript_cost_note }}</p>
    </div>
</div>

<script>
    // Form validation and duplicate submission prevention
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        let isSubmitting = false; // Flag to prevent duplicate submissions
        let validationDisabled = false; // Flag to disable validation if needed

        // Check if user has disabled validation before
        if (localStorage.getItem('disableFormValidation') === 'true') {
            validationDisabled = true;
            console.log('🔧 Form validation disabled by user preference');
        }

        if (form) {
            // Add click listeners to checkboxes for better interaction
            const checkboxes = document.querySelectorAll('input[name="academic_years"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    console.log(`Checkbox changed: ${this.value} = ${this.checked}`);

                    // Update submit button state
                    const checkedCount = document.querySelectorAll('input[name="academic_years"]:checked').length;
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        if (checkedCount > 0) {
                            submitBtn.disabled = false;
                            submitBtn.style.opacity = '1';
                        } else {
                            submitBtn.disabled = false; // Keep enabled for better UX
                            submitBtn.style.opacity = '0.7';
                        }
                    }
                });
            });
            form.addEventListener('submit', function(event) {
                // Prevent duplicate submissions
                if (isSubmitting) {
                    event.preventDefault();
                    console.log('Form submission blocked - already submitting');
                    return false;
                }

                // Skip validation if disabled
                if (validationDisabled) {
                    console.log('🔧 Validation disabled - allowing form submission');
                    isSubmitting = true;
                    return true;
                }

                // More robust checkbox validation
                const checkboxes = document.querySelectorAll('input[name="academic_years"]');
                const checkedBoxes = document.querySelectorAll('input[name="academic_years"]:checked');

                console.log('Total checkboxes found:', checkboxes.length);
                console.log('Checked checkboxes found:', checkedBoxes.length);

                // Debug: Log each checkbox state
                checkboxes.forEach((checkbox, index) => {
                    console.log(`Checkbox ${index}: value="${checkbox.value}", checked=${checkbox.checked}`);
                });

                // TEMPORARY FIX: Disable JavaScript validation to allow form submission
                // The backend validation is working correctly, so we'll rely on that
                if (checkedBoxes.length === 0) {
                    console.log('⚠️ No checkboxes detected as checked, but allowing submission anyway');
                    console.log('Backend validation will handle this properly');

                    // Show warning but don't prevent submission
                    const allowSubmit = confirm('No academic years detected as selected.\n\nClick "OK" to submit anyway (backend will validate), or "Cancel" to check your selection.');

                    if (!allowSubmit) {
                        event.preventDefault();
                        debugForm();
                        return false;
                    }

                    console.log('User chose to submit anyway - proceeding');
                }

                console.log('Form validation passed - proceeding with submission');

                // Set submitting flag
                isSubmitting = true;

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

                    // Prevent any further clicks
                    submitBtn.style.pointerEvents = 'none';
                }

                // Add a small delay to ensure form data is properly captured
                setTimeout(() => {
                    console.log('Form submission proceeding after delay...');
                }, 100);

                // Disable all form inputs to prevent changes
                const inputs = form.querySelectorAll('input, button');
                inputs.forEach(input => {
                    if (input.type !== 'submit') {
                        input.disabled = true;
                    }
                });

                console.log('Form submission proceeding...');
            });

            // Prevent multiple rapid clicks on submit button
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                let clickCount = 0;
                submitBtn.addEventListener('click', function(event) {
                    clickCount++;
                    if (clickCount > 1) {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('Multiple clicks detected - blocking');
                        return false;
                    }
                });
            }
        }
    });

    // Debug function to help troubleshoot form issues
    function debugForm() {
        const debugInfo = document.getElementById('debugInfo');
        const debugContent = document.getElementById('debugContent');

        const checkboxes = document.querySelectorAll('input[name="academic_years"]');
        const checkedBoxes = document.querySelectorAll('input[name="academic_years"]:checked');

        let debugHtml = `
            <p><strong>Total checkboxes found:</strong> ${checkboxes.length}</p>
            <p><strong>Checked checkboxes:</strong> ${checkedBoxes.length}</p>
            <p><strong>Checkbox details:</strong></p>
            <ul>
        `;

        checkboxes.forEach((checkbox, index) => {
            debugHtml += `<li>Checkbox ${index + 1}: "${checkbox.value}" - ${checkbox.checked ? 'CHECKED' : 'NOT CHECKED'}</li>`;
        });

        debugHtml += `
            </ul>
            <p><strong>Form action:</strong> ${document.querySelector('form').action}</p>
            <p><strong>Form method:</strong> ${document.querySelector('form').method}</p>
        `;

        // Add bypass buttons
        debugHtml += `
            <div class="mt-2">
                <button type="button" class="btn btn-warning btn-sm" onclick="bypassValidation()">
                    <i class="fas fa-exclamation-triangle"></i> Force Submit (One Time)
                </button>
                <button type="button" class="btn btn-danger btn-sm ml-2" onclick="disableValidationPermanently()">
                    <i class="fas fa-ban"></i> Disable Validation (Permanent)
                </button>
            </div>
        `;

        debugContent.innerHTML = debugHtml;
        debugInfo.style.display = 'block';
    }

    // Bypass validation and submit form directly (but follow normal workflow)
    function bypassValidation() {
        const form = document.querySelector('form');
        if (form) {
            // Remove all event listeners by cloning the form
            const newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);

            // Submit the new form without validation but follow normal workflow
            console.log('Bypassing validation and submitting form (normal workflow)...');
            newForm.submit();
        }
    }

    // Permanently disable validation for this user
    function disableValidationPermanently() {
        if (confirm('This will permanently disable form validation for future visits.\n\nAre you sure?')) {
            localStorage.setItem('disableFormValidation', 'true');
            alert('Form validation disabled. The page will reload.');
            location.reload();
        }
    }
</script>
{% endblock %}
