#!/usr/bin/env python3
"""
Database migration script to remove payment_proof_filename column
from transcript_requests table since we're using MoMo payments now.
"""

import pymysql
from simple_database_service import get_db_connection

def remove_payment_proof_column():
    """Remove payment_proof_filename column from transcript_requests table"""
    try:
        print("🔄 Starting database migration to remove payment_proof_filename column...")
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check if column exists first
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'ines_transcript_system' 
                AND TABLE_NAME = 'transcript_requests' 
                AND COLUMN_NAME = 'payment_proof_filename'
            """)
            
            column_exists = cursor.fetchone()
            
            if column_exists:
                print("📋 Found payment_proof_filename column, removing it...")
                
                # Remove the column
                cursor.execute("""
                    ALTER TABLE transcript_requests 
                    DROP COLUMN payment_proof_filename
                """)
                
                conn.commit()
                print("✅ Successfully removed payment_proof_filename column")
                
                # Verify removal
                cursor.execute("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = 'ines_transcript_system' 
                    AND TABLE_NAME = 'transcript_requests' 
                    AND COLUMN_NAME = 'payment_proof_filename'
                """)
                
                if not cursor.fetchone():
                    print("✅ Verified: payment_proof_filename column has been removed")
                else:
                    print("❌ Error: Column still exists after removal attempt")
                    
            else:
                print("ℹ️  payment_proof_filename column does not exist, no action needed")
                
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False
        
    return True

def show_table_structure():
    """Show current table structure for verification"""
    try:
        print("\n📋 Current transcript_requests table structure:")
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'ines_transcript_system' 
                AND TABLE_NAME = 'transcript_requests'
                ORDER BY ORDINAL_POSITION
            """)
            
            columns = cursor.fetchall()
            
            print("Column Name | Data Type | Nullable | Default")
            print("-" * 50)
            for col in columns:
                print(f"{col[0]:<20} | {col[1]:<10} | {col[2]:<8} | {col[3] or 'NULL'}")
                
    except Exception as e:
        print(f"❌ Error showing table structure: {e}")

if __name__ == "__main__":
    print("🚀 Database Migration: Remove Payment Proof Column")
    print("=" * 50)
    
    # Show current structure
    show_table_structure()
    
    # Perform migration
    success = remove_payment_proof_column()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        # Show updated structure
        show_table_structure()
    else:
        print("\n❌ Migration failed!")
