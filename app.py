import os
import json
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, session, flash, send_from_directory, send_file, jsonify
import secrets
from werkzeug.utils import secure_filename
from flask_mail import Mail, Message
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Email, To, Content, HtmlContent
from dotenv import load_dotenv
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from functools import wraps
import urllib.parse
from translations import translations, get_translation, get_current_language, set_language
import random
import re
import pymysql
import logging
logging.basicConfig(level=logging.INFO)

# Import SIMPLIFIED database service - Updated for new schema
from simple_database_service import *

# Enhanced finance service functions are now integrated into simple_database_service

# Faculty notification service removed - simplified faculty interface

# Import simple academic years service
try:
    from simple_academic_years import (
        get_student_available_academic_years, 
        validate_academic_year_request, 
        get_current_academic_year,
        is_current_academic_year,
        get_department_program_duration,
        get_department_academic_info
    )
    ENROLLMENT_SERVICE_AVAILABLE = True
    logging.info("Simple academic years service loaded")

    # Wrapper functions to match expected interface
    def get_student_available_years(student_id):
        return get_student_available_academic_years(student_id)

    def validate_student_year_request(student_id, academic_year):
        return validate_academic_year_request(student_id, academic_year)

except ImportError as e:
    logging.error(f"Simple academic years service not available: {e}")
    ENROLLMENT_SERVICE_AVAILABLE = False

    # Create dummy functions if service not available
    def get_student_available_years(student_id):
        # Fallback: return recent years (excluding current year)
        current_year = 2025
        years = []
        for year in range(2020, current_year):
            years.append(f"{year}-{year+1}")
        return years

    def validate_student_year_request(student_id, academic_year):
        # Fallback: allow all years except current year
        current_year = 2025
        if f"{current_year}-{current_year+1}" == academic_year:
            return False
        return True

    def get_current_academic_year():
        current_year = 2025
        return f"{current_year}-{current_year+1}"

    def is_current_academic_year(academic_year_str):
        return academic_year_str == get_current_academic_year()

# Import Redis services (optional)
try:
    from redis_service import get_redis_service, cache_result, init_redis_service
    from redis_session import create_redis_session_interface, create_session_data, get_session_info
    REDIS_AVAILABLE = True
    logging.info("Redis modules loaded successfully")
except ImportError as e:
    logging.error(f"Redis not available: {e}")
    logging.info("Application will run without Redis caching")
    REDIS_AVAILABLE = False

    # Create dummy functions for Redis functionality
    def get_redis_service():
        return None

    def cache_result(expire_seconds=300, key_prefix="func"):
        def decorator(func):
            return func  # No caching, just return original function
        return decorator

    def init_redis_service(*args, **kwargs):
        return None

    def create_redis_session_interface(app):
        return None

# Load environment variables
load_dotenv()

# Decorator for login required
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('login_page'))
        return f(*args, **kwargs)
    return decorated_function

# Decorator for finance role required
def finance_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if session.get('role') != 'finance':
            flash('Unauthorized access', 'error')
            return redirect(url_for('login_page'))
        return f(*args, **kwargs)
    return decorated_function

# Decorator for faculty role required
def faculty_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if session.get('role') != 'faculty':
            flash('Unauthorized access', 'error')
            return redirect(url_for('login_page'))
        return f(*args, **kwargs)
    return decorated_function

# Debug logging for environment variables
logging.info("\n=== Environment Variables Check ===")
logging.info(f"MAIL_SERVER: {os.getenv('MAIL_SERVER')}")
logging.info(f"MAIL_PORT: {os.getenv('MAIL_PORT')}")
logging.info(f"MAIL_USERNAME: {os.getenv('MAIL_USERNAME')}")
logging.info(f"MAIL_DEFAULT_SENDER: {os.getenv('MAIL_DEFAULT_SENDER')}")
logging.info("MAIL_PASSWORD: [HIDDEN]")
logging.info("================================\n")

# Create Flask application
app = Flask(__name__, instance_relative_config=True)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size
app.config['USERS_CSV'] = os.path.join(app.instance_path, 'users.csv')
app.config['TRANSCRIPT_PRICE'] = 1000  # Price per transcript in RWF
# DATABASE_FILE removed - now using MySQL database: ines_transcript_system

# Redis Configuration
app.config['REDIS_HOST'] = os.getenv('REDIS_HOST', 'localhost')
app.config['REDIS_PORT'] = int(os.getenv('REDIS_PORT', 6379))
app.config['REDIS_DB'] = int(os.getenv('REDIS_DB', 0))
app.config['REDIS_PASSWORD'] = os.getenv('REDIS_PASSWORD', None)
app.config['SESSION_PERMANENT'] = True
app.config['SESSION_KEY_PREFIX'] = 'ines:session:'
app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24 hours in seconds

# Email configuration
EMAIL_HOST = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('MAIL_PORT', '587'))
EMAIL_USERNAME = os.getenv('MAIL_USERNAME')
EMAIL_PASSWORD = os.getenv('MAIL_PASSWORD')
EMAIL_FROM = os.getenv('MAIL_DEFAULT_SENDER')

# Initialize Redis (optional)
if REDIS_AVAILABLE:
    logging.info("\n=== Initializing Redis ===")
    try:
        init_redis_service(
            host=app.config['REDIS_HOST'],
            port=app.config['REDIS_PORT'],
            db=app.config['REDIS_DB'],
            password=app.config['REDIS_PASSWORD']
        )
        logging.info("Redis service initialized")

        # For now, skip Redis session interface due to Flask compatibility issues
        # Redis will still be used for caching, which provides the main performance benefits
        logging.info("Using Redis for caching only (session interface disabled for compatibility)")

    except Exception as e:
        logging.error(f"Redis initialization warning: {e}")
        logging.info("Continuing with default Flask sessions")
else:
    logging.info("Redis not available - using default Flask sessions")

# Initialize Flask-Mail
mail = Mail(app)

# Initialize SendGrid
sg = SendGridAPIClient(os.getenv('SENDGRID_API_KEY'))

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# JSON database initialization removed - now using MySQL database: ines_transcript_system
# All data is stored in MySQL tables: new_users, students, departments, faculties, new_transcript_requests, etc.

# JSON database helper functions removed - now using MySQL database: ines_transcript_system
# All database operations are handled by new_database_service.py

def get_user_by_credentials(email, password, department, role):
    """Validate user credentials against MySQL database"""
    try:
        # Use the new database service authentication
        user = authenticate_user(email, password, department, role)
        if user:
            return {
                'id': user['reg_no'],  # Use reg_no as the id for session compatibility
                'email': user['email'],
                'role': user['role'],
                'name': user['name'],
                'department': user.get('department_name') or user.get('faculty_name') or department
            }
        return None
    except Exception as e:
        logging.error(f"Error authenticating user: {e}")
        return None

# Use MySQL database service function directly - no need to redefine
# get_requests_by_student_id is now imported from new_database_service

def get_pending_requests_for_finance():
    """Get all pending requests for finance approval - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        return [req for req in all_requests if req['status'] == 'pending_finance']
    except Exception as e:
        logging.error(f"Error getting pending finance requests: {e}")
        return []

def get_approved_requests_for_finance():
    """Get all approved requests by finance - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        return [req for req in all_requests if req['status'] in ['approved_finance', 'completed']]
    except Exception as e:
        logging.error(f"Error getting approved finance requests: {e}")
        return []

def get_rejected_requests_for_finance():
    """Get all rejected requests by finance - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        return [req for req in all_requests if req['status'] == 'rejected']
    except Exception as e:
        logging.error(f"Error getting rejected finance requests: {e}")
        return []

def get_pending_requests_for_faculty():
    """Get all pending requests for faculty (approved by finance) - MySQL version"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.department_name as department
                FROM transcript_requests tr
                JOIN users u ON tr.student_id = u.id
                WHERE tr.payment_status = 'paid' AND tr.completed_at IS NULL
                ORDER BY tr.finance_confirmed_at DESC
            """)
            requests = cursor.fetchall()
            
            result = []
            for req in requests:
                result.append({
                    'id': str(req['id']),
                    'student_id': req['student_id'],
                    'student_name': req['student_name'],
                    'department': req.get('department', 'Unknown'),
                    'academic_years': json.loads(req['academic_years']),
                    'total_price': float(req['total_amount']),
                    'payment_status': req['payment_status'],
                    'date': req['created_at'].strftime('%Y-%m-%d'),
                    'approved_date': req['finance_confirmed_at'].strftime('%Y-%m-%d') if req['finance_confirmed_at'] else None
                })
            return result
    except Exception as e:
        logging.error(f"Error getting pending faculty requests: {e}")
        return []

def get_completed_requests_for_faculty():
    """Get all completed requests (transcript uploaded) - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        return [req for req in all_requests if req['status'] == 'completed']
    except Exception as e:
        logging.error(f"Error getting completed faculty requests: {e}")
        return []

def get_transcripts():
    """Get all uploaded transcripts - MySQL version"""
    try:
        # Get completed requests from MySQL database
        all_requests = get_all_requests()  # From new_database_service
        transcripts = []
        for req in all_requests:
            if req['status'] == 'completed':
                transcripts.append({
                    'id': req['id'],
                    'student_id': req['student_id'],
                    'student_name': req['student_name'],
                    'academic_years': req['academic_years'],
                    'filename': f"transcript_{req['id']}.pdf",  # Standard filename
                    'upload_date': req.get('approved_date', req['date'])
                })
        return transcripts
    except Exception as e:
        logging.error(f"Error getting transcripts: {e}")
        return []

def get_request_by_id(request_id):
    """Get a specific request by ID - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        for req in all_requests:
            if str(req['id']) == str(request_id):
                return req
        return None
    except Exception as e:
        logging.error(f"Error getting request by ID: {e}")
        return None

# update_request_status is now imported from new_database_service and uses MySQL

def add_transcript_legacy(request_id, filename):
    """Legacy function - Add a transcript to the database - MySQL version
    NOTE: This function is deprecated. Use add_transcript from new_database_service instead.
    """
    try:
        # Update request status to completed using MySQL database service
        success = update_request_status(request_id, 'completed')
        if success:
            logging.info(f"✅ Transcript added for request {request_id}: {filename}")
            return True
        else:
            logging.error(f"❌ Failed to update request {request_id} to completed status")
            return False
    except Exception as e:
        logging.error(f"Error adding transcript: {e}")
        return False

def send_email_notification(to_email, subject, template_data):
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_FROM
        msg['To'] = to_email
        msg['Subject'] = subject

        # Create HTML content
        html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #003366; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
                    .button {{ display: inline-block; padding: 10px 20px; background-color: #003366; color: white; text-decoration: none; border-radius: 5px; }}
                    .status-badge {{ 
                        display: inline-block;
                        padding: 5px 10px;
                        border-radius: 15px;
                        font-size: 12px;
                        font-weight: bold;
                        margin-left: 10px;
                    }}
                    .status-pending {{ background-color: #ffd700; color: #000; }}
                    .status-approved {{ background-color: #28a745; color: #fff; }}
                    .status-rejected {{ background-color: #dc3545; color: #fff; }}
                    .status-completed {{ background-color: #17a2b8; color: #fff; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>INES-Ruhengeri</h1>
                        <p>Transcript System</p>
                    </div>
                    <div class="content">
                        {template_data['content']}
                    </div>
                    <div class="footer">
                        <p>This is an automated message. Please do not reply to this email.</p>
                        <p>© {datetime.now().year} INES-Ruhengeri. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        """
        
        msg.attach(MIMEText(html_content, 'html'))

        # Connect to SMTP server and send email
        with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
            server.starttls()
            server.login(EMAIL_USERNAME, EMAIL_PASSWORD)
            server.send_message(msg)
            
        logging.info(f"Email sent successfully to {to_email}")
        return True
    except Exception as e:
        logging.error(f"Error sending email: {str(e)}")
        logging.error(f"Error type: {type(e)}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")
        return False

def get_next_request_id():
    """Get the next available request ID - MySQL version"""
    try:
        all_requests = get_all_requests()  # From new_database_service
        if not all_requests:
            return 1

        # Get all numeric IDs
        numeric_ids = []
        for req in all_requests:
            try:
                req_id = req['id']
                # Handle both string and integer IDs
                if isinstance(req_id, int):
                    numeric_ids.append(req_id)
                elif isinstance(req_id, str) and req_id.isdigit():
                    numeric_ids.append(int(req_id))
            except (ValueError, TypeError, AttributeError):
                continue

        # If no numeric IDs found, start from 1
        if not numeric_ids:
            return 1

        return max(numeric_ids) + 1
    except Exception as e:
        logging.error(f"Error getting next request ID: {e}")
        return 1

def clear_requests():
    """Clear all existing requests from the MySQL database"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM transcript_requests")
                connection.commit()
                logging.info("✅ All requests cleared from MySQL database")
    except Exception as e:
        logging.error(f"Error clearing requests: {e}")

def get_department_emails(role):
    """Get all email addresses for a specific department role - MySQL version"""
    emails = []
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Try to get emails for the requested role
                cursor.execute("""
                    SELECT email FROM users
                    WHERE role = %s AND is_active = 1
                """, (role,))
                results = cursor.fetchall()
                emails = [row[0] for row in results]

                logging.info(f"📧 Found {len(emails)} {role} users")

    except Exception as e:
        logging.error(f"❌ Error getting {role} emails from MySQL: {e}")

    return emails

def notify_finance_staff(request_data):
    """Send notification to finance staff about new requests"""
    logging.info(f"🔔 Starting finance notification for request {request_data.get('id', 'Unknown')}")

    try:
        template_data = {
            'content': f"""
                <h2>🆕 New Transcript Request Submitted</h2>
                <p>Dear Finance Team,</p>
                <p>A new transcript request has been submitted and requires your review and approval.</p>

                <h3>📋 Request Details:</h3>
                <ul>
                    <li><strong>Request ID:</strong> {request_data.get('id', 'N/A')}</li>
                    <li><strong>Student Name:</strong> {request_data.get('student_name', 'N/A')}</li>
                    <li><strong>Student ID:</strong> {request_data.get('student_id', 'N/A')}</li>
                    <li><strong>Department:</strong> {request_data.get('department', 'N/A')}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
                    <li><strong>Total Amount:</strong> {request_data.get('total_price', 0):,.0f} RWF</li>
                    <li><strong>Payment Method:</strong> {request_data.get('payment_method', 'N/A')}</li>
                    <li><strong>Submission Time:</strong> {request_data.get('date', 'N/A')}</li>
                    <li><strong>Status:</strong> 🟡 Pending Finance Review</li>
                </ul>

                <h3>📝 Action Required:</h3>
                <ol>
                    <li>Log in to the finance dashboard</li>
                    <li>Review payment proof and student details</li>
                    <li>Verify school fees payment status</li>
                    <li>Approve (send to faculty) or Reject (send back to student)</li>
                </ol>

                <p><strong>⏰ Priority:</strong> Please review within 24 hours</p>
                <p><strong>📊 Dashboard:</strong> Access your finance dashboard to process this request</p>

                <p>Best regards,<br>
                INES-Ruhengeri Transcript System</p>
            """
        }

        # Get finance emails from database and add hardcoded backup
        finance_emails = get_department_emails('finance')
        if not finance_emails:
            finance_emails = ['<EMAIL>']  # Fallback
        
        logging.info(f"📧 Sending finance notifications to: {finance_emails}")

        # Send to all finance staff
        for email in finance_emails:
            logging.info(f"📤 Sending notification to: {email}")
            try:
                success = send_email_notification(
                    email,
                    'New Transcript Request - Finance Review Required - INES',
                    template_data
                )
                if success:
                    logging.info(f"✅ Successfully sent to: {email}")
                else:
                    logging.error(f"❌ Failed to send to: {email}")
            except Exception as email_error:
                logging.error(f"❌ Error sending to {email}: {email_error}")

        logging.info(f"✅ Finance notification completed for request {request_data.get('id', 'N/A')}")

    except Exception as e:
        logging.error(f"❌ Error in notify_finance_staff: {e}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")

def notify_student_approval(request_data):
    """Send notification to student about request approval"""
    try:
        student_email = request_data.get('student_email') or request_data.get('email')
        if not student_email:
            # Try to get email from database using student_id
            try:
                with get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT email FROM users WHERE reg_no = %s", (request_data.get('student_id'),))
                    result = cursor.fetchone()
                    if result:
                        student_email = result[0]
            except Exception as e:
                logging.error(f"❌ Error getting student email: {e}")
                return
        
        if not student_email:
            logging.error(f"❌ No email found for student approval notification")
            return
            
        template_data = {
            'content': f"""
                <h2>🎉 Transcript Request Approved!</h2>
                <p>Dear {request_data.get('student_name', 'Student')},</p>
                <p>Great news! Your transcript request has been approved by the finance office and is now being processed by the faculty.</p>

                <h3>📋 Request Details:</h3>
                <ul>
                    <li><strong>Request ID:</strong> {request_data.get('id', 'N/A')}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
                    <li><strong>Approval Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                    <li><strong>Status:</strong> ✅ Approved - Sent to Faculty</li>
                </ul>

                <h3>📝 Next Steps:</h3>
                <ol>
                    <li>Your request is now with the faculty for transcript preparation</li>
                    <li>You will receive another email when your transcript is ready</li>
                    <li>You can track the progress in your student dashboard</li>
                </ol>

                <p><strong>Estimated Processing Time:</strong> 3-5 business days</p>

                <p>Thank you for your patience!</p>

                <p>Best regards,<br>
                INES-Ruhengeri Finance Department</p>
            """
        }

        success = send_email_notification(
            student_email,
            'Transcript Request Approved - Sent to Faculty - INES',
            template_data
        )
        if success:
            logging.info(f"✅ Approval notification sent to student: {student_email}")
        else:
            logging.error(f"❌ Failed to send approval notification to: {student_email}")
    except Exception as e:
        logging.error(f"❌ Error sending student approval notification: {e}")

def notify_student_rejection(request_data, rejection_reason=None):
    """Send notification to student about request rejection"""
    try:
        # Use provided rejection reason or fallback to request data or default
        final_rejection_reason = rejection_reason or request_data.get('rejection_reason', 'School fees payment required')

        template_data = {
            'content': f"""
                <h2>❌ Transcript Request Rejected</h2>
                <p>Dear {request_data['student_name']},</p>
                <p>We have reviewed your transcript request and unfortunately it has been <strong>rejected</strong> by our finance department.</p>

                <h3>📋 Request Details:</h3>
                <ul>
                    <li><strong>Request ID:</strong> {request_data['id']}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data['academic_years'])}</li>
                    <li><strong>Review Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                    <li><strong>Status:</strong> Rejected</li>
                </ul>

                <h3>❌ Reason for Rejection:</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #dc3545; border-radius: 5px; margin: 15px 0;">
                    <strong style="color: #dc3545;">{final_rejection_reason}</strong>
                </div>

                <h3>📝 Next Steps:</h3>
                <ol>
                    <li>Please resolve the issue mentioned above</li>
                    <li>Complete any outstanding school fees payments if required</li>
                    <li>Submit a new transcript request once the issue is resolved</li>
                    <li>Contact the finance office if you need clarification</li>
                </ol>

                <p><strong>Contact Information:</strong><br>
                Finance Office: <EMAIL><br>
                Phone: +250 788 123 456</p>

                <p>We apologize for any inconvenience and look forward to processing your request once the issue is resolved.</p>

                <p>Best regards,<br>
                INES-Ruhengeri Finance Department</p>
            """
        }

        send_email_notification(
            request_data['email'],
            'Transcript Request Update - INES-Ruhengeri',
            template_data
        )
        logging.info(f"✅ Rejection notification sent to student: {request_data['email']} (Reason: {final_rejection_reason})")
    except Exception as e:
        logging.error(f"❌ Error sending student rejection notification: {e}")

def notify_faculty_staff(request_data):
    """Send notification to specific faculty based on student department"""
    try:
        student_department = request_data.get('department', 'Unknown')
        
        # Get faculty emails from database based on department
        faculty_emails = []
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT u.email FROM users u 
                    WHERE u.role = 'faculty' AND u.is_active = 1
                    AND (u.faculty_name LIKE %s OR u.department_name = %s)
                """, (f'%{student_department}%', student_department))
                results = cursor.fetchall()
                faculty_emails = [row[0] for row in results]
        except Exception as e:
            logging.error(f"Error getting faculty emails from database: {e}")
        
        # Fallback to hardcoded mapping if no emails found
        if not faculty_emails:
            faculty_email_map = {
                'Computer Science': ['<EMAIL>'],
                'Statistics Applied to Economy': ['<EMAIL>'],
                'Civil Engineering': ['<EMAIL>'],
                'Architecture': ['<EMAIL>'],
                'Biotechnologies': ['<EMAIL>'],
                'Water Engineering': ['<EMAIL>'],
                'Surveying': ['<EMAIL>'],
                'Land Survey': ['<EMAIL>'],
                'Masonry': ['<EMAIL>'],
                'Welding': ['<EMAIL>'],
                'Domestic Plumbing': ['<EMAIL>'],
                'Cooperatives Management': ['<EMAIL>'],
                'Entrepreneurship and SME\'s Management': ['<EMAIL>'],
                'French and English': ['<EMAIL>'],
                'Law': ['<EMAIL>']
            }
            faculty_emails = faculty_email_map.get(student_department, [])
        
        if not faculty_emails:
            logging.error(f"No faculty emails found for department: {student_department}")
            return
        
        template_data = {
            'content': f"""
                <h2>📋 New Approved Transcript Request</h2>
                <p>Dear Faculty Staff,</p>
                <p>A transcript request has been approved by finance and requires your attention for transcript preparation.</p>

                <h3>📋 Request Details:</h3>
                <ul>
                    <li><strong>Request ID:</strong> {request_data.get('id', 'N/A')}</li>
                    <li><strong>Student Name:</strong> {request_data.get('student_name', 'N/A')}</li>
                    <li><strong>Student ID:</strong> {request_data.get('student_id', 'N/A')}</li>
                    <li><strong>Department:</strong> {student_department}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
                    <li><strong>Approval Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                </ul>

                <h3>📝 Action Required:</h3>
                <ol>
                    <li>Log in to the faculty dashboard</li>
                    <li>Prepare the official transcript</li>
                    <li>Upload the transcript PDF file</li>
                    <li>Mark the request as completed</li>
                </ol>

                <p><strong>Priority:</strong> Please process within 3-5 business days</p>

                <p>Best regards,<br>
                INES-Ruhengeri Finance Department</p>
            """
        }
        
        # Send to specific faculty only
        for email in faculty_emails:
            try:
                send_email_notification(
                    email,
                    f'New Approved Transcript Request - {student_department} - INES',
                    template_data
                )
                logging.info(f"✅ Faculty notification sent to: {email} for {student_department}")
            except Exception as e:
                logging.error(f"❌ Error sending to {email}: {e}")
                
    except Exception as e:
        logging.error(f"❌ Error sending faculty notification: {e}")

def notify_student_transcript_ready(request_data):
    """Send notification to student when transcript is ready for download"""
    try:
        template_data = {
            'content': f"""
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div style="background-color: #1a237e; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                        <h2 style="margin: 0; font-size: 24px;">🎉 Your Transcript is Ready!</h2>
                    </div>

                    <div style="padding: 30px;">
                        <p style="font-size: 16px; color: #333; margin-bottom: 20px;">
                            Dear <strong>{request_data.get('student_name', 'Student')}</strong>,
                        </p>

                        <p style="font-size: 16px; color: #333; margin-bottom: 25px;">
                            Excellent news! Your official academic transcript has been successfully processed by the Faculty Office and is now ready for download.
                        </p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                            <h3 style="color: #1a237e; margin-top: 0; font-size: 18px;">📋 Request Summary</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Request ID:</td>
                                    <td style="padding: 8px 0; color: #333;">#{request_data.get('id', 'N/A')}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Academic Years:</td>
                                    <td style="padding: 8px 0; color: #333;">{', '.join(request_data.get('academic_years', []))}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Issue Date:</td>
                                    <td style="padding: 8px 0; color: #333;">{datetime.now().strftime('%B %d, %Y at %I:%M %p')}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Status:</td>
                                    <td style="padding: 8px 0; color: #28a745; font-weight: bold;">✅ Completed & Ready</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 6px; border-left: 4px solid #1a237e; margin-bottom: 25px;">
                            <h3 style="color: #1a237e; margin-top: 0; font-size: 18px;">📥 Download Instructions</h3>
                            <ol style="color: #333; line-height: 1.6; margin: 0; padding-left: 20px;">
                                <li><strong>Login</strong> to your student dashboard at the INES Transcript System</li>
                                <li><strong>Navigate</strong> to "View Downloads" from the main menu</li>
                                <li><strong>Locate</strong> your completed request (Request #{request_data.get('id', 'N/A')})</li>
                                <li><strong>Click</strong> the download button to get your official transcript PDF</li>
                            </ol>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107; margin-bottom: 25px;">
                            <p style="margin: 0; color: #856404; font-weight: bold;">
                                ⚠️ <strong>Important:</strong> Your transcript can be downloaded once. Please keep it secure as it contains your official academic records.
                            </p>
                        </div>

                        <div style="text-align: center; margin-bottom: 25px;">
                            <p style="color: #666; font-size: 14px; margin: 0;">
                                Need help? Contact the Faculty Office or visit the Administration Building.
                            </p>
                        </div>

                        <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
                            <p style="color: #666; font-size: 14px; margin: 0;">
                                Best regards,<br>
                                <strong>INES-Ruhengeri Faculty Office</strong><br>
                                📧 <EMAIL> | 📞 +250 788 123 456
                            </p>
                        </div>
                    </div>
                </div>
            """
        }

        # Get student email if not in request_data
        student_email = request_data.get('email')
        if not student_email:
            logging.warning("⚠️ No email in request_data, attempting to get from database...")
            try:
                from simple_database_service import get_db_connection
                with get_db_connection() as connection:
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            SELECT u.email FROM users u
                            JOIN transcript_requests tr ON u.id = tr.student_id
                            WHERE tr.id = %s
                        """, (request_data.get('id'),))
                        result = cursor.fetchone()
                        if result:
                            student_email = result[0]
                            logging.info(f"✅ Found student email in database: {student_email}")
                        else:
                            logging.error("❌ No student email found in database")
            except Exception as e:
                logging.error(f"❌ Error getting student email from database: {e}")

        if student_email:
            success = send_email_notification(
                student_email,
                'Your Transcript is Ready for Download - INES-Ruhengeri',
                template_data
            )
            if success:
                logging.info(f"✅ Transcript ready notification sent to student: {student_email}")
            else:
                logging.error(f"❌ Failed to send notification to: {student_email}")
        else:
            logging.error("❌ No student email available for notification")

    except Exception as e:
        logging.error(f"❌ Error in notify_student_transcript_ready: {e}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")

def validate_payment(request):
    """Validate payment conditions for transcript request"""
    logging.info("Entering validate_payment function.") # Debug print
    try:
        # Check if payment amount matches total price
        if float(request.get('total_price', 0)) <= 0:
            logging.info("Validation failed: Invalid total price.") # Debug print
            return False
            
        # Verify payment method
        valid_payment_methods = ['mobile_money', 'bank_transfer', 'cash']
        if request.get('payment_method') not in valid_payment_methods:
            logging.info(f"Validation failed: Invalid payment method - {request.get('payment_method')}") # Debug print
            return False
            
        # Validate school fees
        logging.info("Calling validate_school_fees...") # Debug print
        if not validate_school_fees(request):
            logging.info("Validation failed: School fees not paid.") # Debug print
            return False
            
        logging.info("Payment validation successful.") # Debug print
        return True
        
    except Exception as e:
        logging.error(f"Payment validation error: {str(e)}")
        return False

def validate_school_fees(request):
    """Validate if student has paid required school fees for requested years"""
    logging.info("Entering validate_school_fees function.") # Debug print
    try:
        # Get student's department
        student_department = request.get('department', '').lower().replace(' ', '_')
        logging.info(f"Student department for fee validation: {student_department}") # Debug print
        
        # Get annual fee for the department from database
        annual_fee = get_department_fee(student_department)
        logging.info(f"Annual fee for {student_department}: {annual_fee}") # Debug print
        if annual_fee <= 0:
            logging.info(f"Validation failed: Invalid or missing fee for department: {student_department}") # Debug print
            return False
            
        # Get student's payment history
        logging.info(f"Getting payment history for student ID: {request.get('student_id')}") # Debug print
        payment_history = get_student_payment_history(request['student_id'])
        logging.info(f"Student payment history: {payment_history}") # Debug print
        
        # Calculate required fees for requested years
        required_fees = {}
        logging.info(f"Requested academic years: {request.get('academic_years', [])}") # Debug print
        for year in request.get('academic_years', []):
            year_num = int(year.split('-')[0])
            required_fees[year_num] = annual_fee
        print(f"Required fees calculated: {required_fees}") # Debug print
        
        # Check if all required fees are paid
        unpaid_years = []
        unpaid_amounts_dict = {}
        print("Checking for unpaid years...") # Debug print
        for year, required_amount in required_fees.items():
            paid_amount = payment_history.get(year, 0)
            print(f"Year: {year}, Required: {required_amount}, Paid: {paid_amount}") # Debug print
            if paid_amount < required_amount:
                unpaid_years.append(year)
                unpaid_amounts_dict[year] = required_amount - paid_amount
                print(f"Insufficient payment for year {year}.") # Debug print
        
        if unpaid_years:
            # Store unpaid years and amounts in the request for display
            request['unpaid_years'] = unpaid_years
            request['unpaid_amounts'] = unpaid_amounts_dict
            print(f"Unpaid years found: {unpaid_years}, Unpaid amounts: {unpaid_amounts_dict}") # Debug print
            return False
                
        print("School fees validation successful.") # Debug print
        return True
    except Exception as e:
        print(f"School fees validation error: {str(e)}")
        import traceback
        print(f"School fees validation traceback: {traceback.format_exc()}") # Debug print
        return False

def get_student_payment_history(student_id):
    """Get student's payment history from MySQL database"""
    print(f"Entering get_student_payment_history for student ID: {student_id}") # Debug print
    try:
        # For now, return empty payment history since we're focusing on transcript system
        # This can be enhanced later with actual payment tracking in MySQL
        print(f"Using simplified payment validation for student {student_id}")
        return {}  # Empty payment history - will be handled by enhanced_finance_service
    except Exception as e:
        print(f"Error getting payment history: {str(e)}")
        import traceback
        print(f"Get student payment history traceback: {traceback.format_exc()}") # Debug print
        return {}

# get_department_fee and update_department_fee are now imported from new_database_service and use MySQL

def validate_request(request):
    """Validate request conditions for transcript request"""
    try:
        # Check if academic years are valid
        current_year = datetime.now().year
        for year in request.get('academic_years', []):
            year_num = int(year.split('-')[0])
            if year_num < 2010 or year_num > current_year:
                return False
        
        # Verify student information
        required_fields = ['student_name', 'student_id', 'email', 'phone', 'department']
        for field in required_fields:
            if not request.get(field):
                return False
        
        # Allow re-processing of requests
        # We'll track request history instead of blocking re-requests
        if request.get('status') in ['approved', 'rejected']:
            # Create a new request ID for the re-request
            request['id'] = str(get_next_request_id())
            request['status'] = 'pending_finance'
            request['previous_request_id'] = request.get('id')  # Track the original request
            request['re_request_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        return True
    except Exception as e:
        print(f"Request validation error: {str(e)}")
        return False

# init_departments function removed - now using MySQL database: ines_transcript_system
# All department data is stored in MySQL departments table

# Homepage route
@app.route('/home')
def homepage():
    return render_template('homepage.html')

# Login page route
@app.route('/login', methods=['GET', 'POST'])
def login_page():
    return render_template('login_page.html')

# Login route (handles form submission)
@app.route('/', methods=['GET', 'POST'])
def login():
    # For GET requests, redirect to homepage
    if request.method == 'GET':
        return redirect(url_for('homepage'))

    error = None
    email = request.form['email']
    password = request.form['password']
    role = request.args.get('role', '')  # Get role from query parameter

    # Get department/faculty based on role
    department = None
    if role == 'student':
        department = request.form.get('department')
    elif role == 'faculty':
        department = request.form.get('faculty')

    print(f"Attempting login with email: {email}, role: {role}, department: {department}")  # Debug log
    
    # Faculty login bypass for specific emails
    if role == 'faculty' and email in ['<EMAIL>', '<EMAIL>'] and password == 'faculty123':
        print(f"Faculty bypass login for: {email}")
        # Create faculty user object
        if email == '<EMAIL>':
            user = {
                'reg_no': 'FAC001',
                'email': email,
                'role': 'faculty',
                'name': 'Faculty Sciences',
                'department': 'Faculty of Sciences and Information Technology'
            }
        else:
            user = {
                'reg_no': 'FAC002', 
                'email': email,
                'role': 'faculty',
                'name': 'Faculty Engineering',
                'department': 'Faculty of Engineering and Technology'
            }
    else:
        user = authenticate_user(email, password, department, role)
    
    print(f"Authentication result: {user is not None}")

    if user:
        print(f"Login successful. User data: {user}")  # Debug log
        # Clear any existing session data first
        session.clear()
        
        # Store user info in session
        session['user_id'] = user['reg_no']  # Use reg_no as user_id for compatibility
        session['email'] = user['email']
        session['role'] = user['role']
        session['name'] = user['name']
        session['department'] = user.get('department') or department or 'N/A'
        session['enrollment_year'] = user.get('enrollment_year', 2021)
        session.permanent = True  # Make session permanent
        print(f"Session data after login: {dict(session)}")  # Debug log

        # Redirect based on role
        if user['role'] == 'student':
            return redirect(url_for('student_dashboard'))
        elif user['role'] == 'finance':
            return redirect(url_for('finance_dashboard'))
        elif user['role'] == 'faculty':
            print(f"Faculty login successful, redirecting to dashboard")
            return redirect(url_for('faculty_dashboard'))
    else:
        print(f"Login failed for email: {email}")  # Debug log
        error = 'Invalid credentials or department/faculty. Please try again.'

    return render_template('login_page.html', error=error)

# Logout route
@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login_page'))

# Student routes
@app.route('/set-language/<lang>')
def change_language(lang):
    print(f"Changing language to: {lang}")  # Debug log
    if lang in translations:
        session['language'] = lang
        print(f"Language set to: {lang}")  # Debug log
        print(f"Current session: {dict(session)}")  # Debug log
    else:
        print(f"Invalid language: {lang}")  # Debug log
    return redirect(request.referrer or url_for('student_dashboard'))

@app.route('/student/dashboard')
def student_dashboard():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))
    # Get user info from session
    student_id = session.get('user_id')
    student_name = session.get('name')
    department = session.get('department')

    # Get language from session, default to English
    language = session.get('language', 'en')
    current_translations = translations[language]

    # Try to get cached dashboard stats (if Redis available)
    cached_stats = None
    if REDIS_AVAILABLE:
        redis_service = get_redis_service()
        if redis_service:
            cached_stats = redis_service.get_cached_dashboard_stats(student_id, 'student')

    if cached_stats:
        # Use cached statistics
        pending_count = cached_stats['pending_count']
        approved_count = cached_stats['approved_count']
        completed_count = cached_stats.get('completed_count', 0)  # Add default value for backward compatibility
        downloaded_count = cached_stats.get('downloaded_count', 0)  # Add default value for backward compatibility
        rejected_count = cached_stats['rejected_count']
        requested_count = cached_stats['requested_count']
        recent_requests = cached_stats['recent_requests']
    else:
        # Get all requests for this student using new database service
        all_requests = get_requests_by_student_id(student_id)

        # Get recent requests for display (most recent 5)
        recent_requests = sorted(all_requests, key=lambda x: x.get('date', ''), reverse=True)[:5]

        # Updated counting logic based on new requirements
        # Use finance dashboard logic for pending count (only unconfirmed requests)
        pending_count = 0
        for req in all_requests:
            status = req['status']
            confirmed = req.get('finance_confirmed_at')

            if status == 'pending_finance':
                pending_count += 1
            elif status == 'pending_confirmation':
                if not confirmed:
                    pending_count += 1  # Only count unconfirmed pending_confirmation requests

        # 2. Approved: Confirmed approved requests from finance (NOT including downloaded)
        approved_count = sum(1 for req in all_requests if req['status'] in ['approved_finance', 'completed'])

        # 3. Rejected: Confirmed rejected requests from finance
        rejected_count = sum(1 for req in all_requests if req['status'] == 'rejected')

        # Additional counts for compatibility
        completed_count = sum(1 for req in all_requests if req.get('completed_at') or req.get('transcript_file'))  # Ready for download
        downloaded_count = sum(1 for req in all_requests if req['status'] == 'done')  # Downloaded transcripts
        requested_count = len(all_requests)

        # Cache the statistics (if Redis available)
        if REDIS_AVAILABLE:
            redis_service = get_redis_service()
            if redis_service:
                stats = {
                    'pending_count': pending_count,
                    'approved_count': approved_count,
                    'completed_count': completed_count,
                    'downloaded_count': downloaded_count,
                    'rejected_count': rejected_count,
                    'requested_count': requested_count,
                    'recent_requests': recent_requests
                }
                redis_service.cache_dashboard_stats(student_id, 'student', stats, 600)  # Cache for 10 minutes
    
    return render_template('student/dashboard.html',
                         student_name=student_name,
                         department=department,
                         requests=recent_requests,
                         translations=current_translations,
                         current_language=language,
                         pending_count=pending_count,
                         approved_count=approved_count,
                         completed_count=completed_count,
                         downloaded_count=downloaded_count,
                         rejected_count=rejected_count,
                         requested_count=requested_count,
                         get_current_language=get_current_language,
                         get_translation=get_translation)

@app.route('/student/request-transcript', methods=['GET', 'POST'])
def request_transcript():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))
    
    # Get language from session, default to English
    language = session.get('language', 'en')
    print(f"Current language in request_transcript: {language}")  # Debug log
    print(f"Current session: {dict(session)}")  # Debug log
    current_translations = translations[language]
    
    if request.method == 'POST':
        print(f"POST request received for transcript request")
        print(f"Form data: {dict(request.form)}")
        print(f"Form keys: {list(request.form.keys())}")

        academic_years = request.form.getlist('academic_years')
        email = request.form.get('email', session.get('email', ''))
        student_id = session.get('user_id')

        print(f"Academic years selected: {academic_years}")
        print(f"Email: {email}")
        print(f"Student ID: {student_id}")

        # Validate required fields
        if not email:
            flash('Email is required. Please try again.', 'error')
            return redirect(url_for('request_transcript'))

        if not academic_years:
            print("❌ BACKEND VALIDATION: No academic years selected")
            print(f"📋 Form data received: {dict(request.form)}")
            print(f"📋 Form keys: {list(request.form.keys())}")
            print(f"📋 getlist('academic_years'): {request.form.getlist('academic_years')}")

            flash(current_translations['select_at_least_one_year'], 'error')
            return redirect(url_for('request_transcript'))

        # Validate academic years against student enrollment and current year restriction
        if ENROLLMENT_SERVICE_AVAILABLE:
            invalid_years = []
            current_year_attempts = []
            
            for year in academic_years:
                if is_current_academic_year(year):
                    current_year_attempts.append(year)
                elif not validate_student_year_request(student_id, year):
                    invalid_years.append(year)

            if current_year_attempts:
                flash(f'You cannot request transcripts for your current academic year: {", ".join(current_year_attempts)}. Please wait until the academic year is completed.', 'error')
                return redirect(url_for('request_transcript'))
                
            if invalid_years:
                flash(f'You cannot request transcripts for years you were not enrolled or have not completed: {", ".join(invalid_years)}. Please contact the administration office if this is an error.', 'error')
                return redirect(url_for('request_transcript'))
        
        # Store request details in session for summary page
        session['transcript_request'] = {
            'academic_years': academic_years,
            'email': email,
            'count': len(academic_years),
            'total_price': len(academic_years) * app.config['TRANSCRIPT_PRICE']
        }

        print(f"Stored in session: {session['transcript_request']}")
        print(f"Redirecting to request_summary")
        
        # Send email notification for request
        try:
            template_data = {
                'content': f"""
                    <h2>{current_translations['transcript_request_received']}</h2>
                    <p>{current_translations['dear']} {session['name']},</p>
                    <p>{current_translations['request_received_message']}</p>
                    
                    <h3>{current_translations['request_details']}:</h3>
                    <ul>
                        <li><strong>{current_translations['academic_years']}:</strong> {', '.join(academic_years)}</li>
                        <li><strong>{current_translations['number_of_transcripts']}:</strong> {len(academic_years)}</li>
                        <li><strong>{current_translations['total_price']}:</strong> {len(academic_years) * app.config.get('TRANSCRIPT_PRICE', 1000)} RWF</li>
                        <li><strong>{current_translations['request_time']}:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                    </ul>
                    
                    <p>{current_translations['proceed_to_payment_message']}</p>
                    
                    <p>{current_translations['best_regards']},<br>
                    INES-Ruhengeri Transcript System</p>
                """
            }
            
            send_email_notification(
                email,
                f"{current_translations['transcript_request_received']} - INES-Ruhengeri",
                template_data
            )
        except Exception as e:
            print(f"Error sending request notification email: {e}")
            # Continue with the request even if email fails
        
        return redirect(url_for('request_summary'))
    
    # For GET request, show the form
    # Get available years from database
    student_id = session.get('user_id')
    
    # Import the function from database service
    from simple_database_service import get_student_available_academic_years
    
    available_years = get_student_available_academic_years(student_id)
    
    if not available_years:
        flash('No completed academic years found. You can only request transcripts for completed years. Contact administration if this is incorrect.', 'warning')
    
    # Get department info
    department_info = {
        'name': session.get('department', 'Unknown'),
        'current_year': 2025
    }
    
    # Get request counts for notifications
    requests = get_requests_by_student_id(session['user_id'])
    # Use finance dashboard logic for pending count
    pending_count = 0
    for req in requests:
        status = req['status']
        confirmed = req.get('finance_confirmed_at')

        if status == 'pending_finance':
            pending_count += 1
        elif status == 'pending_confirmation':
            if not confirmed:
                pending_count += 1  # Only count unconfirmed pending_confirmation requests
    approved_count = sum(1 for req in requests if req['status'] in ['approved_finance', 'completed'])
    requested_count = len(requests)
    
    return render_template('student/request_transcript.html', 
                          available_years=available_years,
                          department_info=department_info,
                          current_academic_year=get_current_academic_year() if ENROLLMENT_SERVICE_AVAILABLE else None,
                          pending_count=pending_count,
                          approved_count=approved_count,
                          requested_count=requested_count,
                          translations=current_translations,
                          current_language=language,
                          get_current_language=get_current_language,
                          get_translation=get_translation)

@app.route('/student/request-summary')
def request_summary():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))
    
    if 'transcript_request' not in session:
        return redirect(url_for('request_transcript'))
    
    request_data = session['transcript_request']
    
    # Get language from session, default to English
    language = session.get('language', 'en')
    current_translations = translations[language]
    
    # Get request counts for notifications
    requests = get_requests_by_student_id(session['user_id'])
    # Use finance dashboard logic for pending count
    pending_count = 0
    for req in requests:
        status = req['status']
        confirmed = req.get('finance_confirmed_at')

        if status == 'pending_finance':
            pending_count += 1
        elif status == 'pending_confirmation':
            if not confirmed:
                pending_count += 1  # Only count unconfirmed pending_confirmation requests
    approved_count = sum(1 for req in requests if req['status'] in ['approved_finance', 'completed'])
    requested_count = len(requests)
    
    return render_template('student/request_summary.html', 
                          request=request_data,
                          translations=current_translations,
                          current_language=language,
                          get_current_language=get_current_language,
                          get_translation=get_translation,
                          pending_count=pending_count,
                          approved_count=approved_count,
                          requested_count=requested_count)

@app.route('/student/payment', methods=['GET', 'POST'])
def payment():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    if 'transcript_request' not in session:
        return redirect(url_for('request_transcript'))

    if request.method == 'POST':
        payment_method = request.form['payment_method']

        # Store payment method in session for proof upload
        request_data = session.get('transcript_request')
        request_data['payment_method'] = payment_method
        session['transcript_request'] = request_data
        print(f"Payment method stored in session: {payment_method}")

        # Redirect to payment proof upload page
        return redirect(url_for('payment_proof'))

    # GET request - show payment method selection page
    request_data = session['transcript_request']

    # Get request counts for notifications
    requests = get_requests_by_student_id(session['user_id'])
    # Use finance dashboard logic for pending count
    pending_count = 0
    for req in requests:
        status = req['status']
        confirmed = req.get('finance_confirmed_at')

        if status == 'pending_finance':
            pending_count += 1
        elif status == 'pending_confirmation':
            if not confirmed:
                pending_count += 1  # Only count unconfirmed pending_confirmation requests
    approved_count = sum(1 for req in requests if req['status'] in ['approved_finance', 'completed'])
    requested_count = len(requests)

    # Get language from session, default to English
    language = session.get('language', 'en')
    current_translations = translations[language]

    return render_template('student/payment.html',
                          request=request_data,
                          pending_count=pending_count,
                          approved_count=approved_count,
                          requested_count=requested_count,
                          translations=current_translations,
                          current_language=language,
                          get_current_language=get_current_language,
                          get_translation=get_translation)

@app.route('/payment-proof')
def payment_proof():
    """Payment proof upload page"""
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    # Check if transcript request data exists
    if 'transcript_request' not in session:
        flash('No transcript request found. Please start a new request.', 'error')
        return redirect(url_for('request_transcript'))

    return render_template('student/payment_proof.html')

@app.route('/submit-payment-proof', methods=['POST'])
def submit_payment_proof():
    """Handle payment proof submission and create transcript request"""
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    # Check if transcript request data exists
    if 'transcript_request' not in session:
        flash('No transcript request found. Please start a new request.', 'error')
        return redirect(url_for('request_transcript'))

    # Check for recent duplicate submissions (within last 5 minutes)
    student_id = session['user_id']
    recent_requests = get_requests_by_student_id(student_id)

    # Check if there's a very recent request (within 5 minutes)
    from datetime import datetime, timedelta
    five_minutes_ago = datetime.now() - timedelta(minutes=5)

    for req in recent_requests:
        req_date_str = req.get('date', '')
        if req_date_str:
            try:
                # Parse the date string (format: YYYY-MM-DD)
                req_date = datetime.strptime(req_date_str, '%Y-%m-%d')
                # If request was created today, check if it's very recent
                if req_date.date() == datetime.now().date():
                    # For today's requests, assume they're recent to prevent duplicates
                    flash('You have already submitted a request recently. Please wait before submitting another request.', 'warning')
                    return redirect(url_for('student_dashboard'))
            except ValueError:
                continue  # Skip if date parsing fails

    # Check if payment proof file was uploaded
    if 'payment_proof' not in request.files:
        flash('Please upload payment proof to continue.', 'error')
        return redirect(url_for('payment_proof'))

    file = request.files['payment_proof']
    if file.filename == '':
        flash('Please select a file to upload.', 'error')
        return redirect(url_for('payment_proof'))

    # Validate file type and size
    allowed_extensions = {'png', 'jpg', 'jpeg', 'pdf'}
    if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        flash('Please upload a valid image file (PNG, JPG) or PDF.', 'error')
        return redirect(url_for('payment_proof'))

    try:
        # Save the uploaded file
        import os
        from werkzeug.utils import secure_filename

        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # Replace / with _ in user ID for safe filename
        safe_user_id = str(session['user_id']).replace('/', '_').replace('\\', '_')
        filename = f"payment_proof_{safe_user_id}_{timestamp}_{filename}"

        # Create upload directory if it doesn't exist
        proof_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'payment_proofs')
        os.makedirs(proof_upload_dir, exist_ok=True)

        file_path = os.path.join(proof_upload_dir, filename)
        file.save(file_path)

        # Get request data from session
        request_data = session.pop('transcript_request')
        payment_method = request_data.get('payment_method', 'mobile_money')
        student_department = session.get('department', '')
        print(f"Creating request with payment method: {payment_method}")

        # Create the request using new database service with payment proof
        print(f"🔄 Creating request for student {session['user_id']} with proof {filename}")
        print(f"📋 Request data: {request_data}")

        new_request = add_request(
            student_reg_no=session['user_id'],
            academic_years=request_data['academic_years'],
            payment_method=payment_method,
            total_price=request_data['total_price'],
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename=filename
        )
        print(f"Request created with payment method: {payment_method}")

        print(f"📝 Request creation result: {new_request}")

        if new_request:
            # Add payment record using new database service
            add_payment(
                student_reg_no=session['user_id'],
                academic_year=','.join(request_data['academic_years']),
                amount=request_data['total_price'],
                payment_method=payment_method,
                request_id=new_request['id'],
                department=student_department
            )
            print(f"Payment record added with method: {payment_method}")
            
            # Ensure request has complete data for notifications
            new_request.update({
                'student_name': session['name'],
                'student_email': session['email'],
                'department': session.get('department', 'Unknown'),
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

            # Send confirmation email to student
            try:
                template_data = {
                    'content': f"""
                        <h2>Transcript Request Submitted Successfully</h2>
                        <p>Dear {session['name']},</p>
                        <p>Your transcript request has been submitted and is being processed.</p>

                        <h3>Request Details:</h3>
                        <ul>
                            <li><strong>Request ID:</strong> {new_request['id']}</li>
                            <li><strong>Academic Years:</strong> {', '.join(request_data['academic_years'])}</li>
                            <li><strong>Total Amount:</strong> {request_data['total_price']:,.0f} RWF</li>
                            <li><strong>Payment Method:</strong> {payment_method}</li>
                            <li><strong>Submission Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                        </ul>

                        <p>Your request is now being processed by the finance office. You will receive updates via email.</p>

                        <p>Best regards,<br>
                        INES-Ruhengeri Transcript System</p>
                    """
                }

                send_email_notification(
                    session['email'],
                    'Transcript Request Submitted - INES-Ruhengeri',
                    template_data
                )
                print(f"✅ Confirmation email sent to student: {session['email']}")
            except Exception as e:
                print(f"❌ Error sending student confirmation email: {e}")

            # Skip finance notification to speed up request submission
            print(f"📝 Request {new_request['id']} created successfully")

            # School fees calculation is now handled in the database service
            print(f"📊 Request {new_request['id']} ready for finance review")
            
            flash('Payment proof uploaded successfully! Your request has been submitted to finance for review.', 'success')
        else:
            flash('Error creating request. Please try again.', 'error')

        # Clear student cache to ensure fresh data on dashboard
        if REDIS_AVAILABLE:
            redis_service = get_redis_service()
            if redis_service:
                redis_service.invalidate_student_cache(session['user_id'])

        return redirect(url_for('student_dashboard'))

    except Exception as e:
        flash(f'Error uploading payment proof: {str(e)}', 'error')
        return redirect(url_for('payment_proof'))

@app.route('/api/student/dashboard-stats')
@login_required
def get_dashboard_stats():
    """API endpoint for real-time dashboard statistics"""
    if session.get('role') != 'student':
        return jsonify({'error': 'Unauthorized'}), 403

    student_id = session['user_id']

    # Debug logging
    print(f"🔄 API called for student: {student_id} at {datetime.now().strftime('%H:%M:%S')}")

    # Get fresh data from database (bypass cache)
    all_requests = get_requests_by_student_id(student_id)
    print(f"📊 Found {len(all_requests)} total requests for student {student_id}")

    # Calculate statistics
    pending_count = sum(1 for req in all_requests if req.get('payment_status') == 'pending')
    approved_count = sum(1 for req in all_requests if req.get('payment_status') == 'paid')
    rejected_count = sum(1 for req in all_requests if req.get('payment_status') == 'failed')
    completed_count = sum(1 for req in all_requests if req.get('completed_at') or req.get('transcript_file'))

    stats = {
        'pending_count': pending_count,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
        'completed_count': completed_count,
        'total_count': len(all_requests),
        'last_updated': datetime.now().isoformat()
    }

    print(f"📊 Student stats: {stats}")
    return jsonify(stats)


# Student view status/history routes removed - these are now in Finance dashboard

@app.route('/student/request-status')
def request_status():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    # Clear any cached data to ensure fresh status
    student_id = session['user_id']
    if REDIS_AVAILABLE:
        redis_service = get_redis_service()
        if redis_service:
            redis_service.invalidate_student_cache(student_id)

    # Get all requests for this student (fresh from database)
    requests = get_requests_by_student_id(student_id)

    # Enhanced formatting for display with detailed status information
    for req in requests:
        # Ensure ID is in the correct format
        try:
            req_id = req['id']
            if isinstance(req_id, str) and req_id.isdigit():
                req['id'] = int(req_id)
            elif isinstance(req_id, int):
                pass  # Already an integer
        except (ValueError, TypeError, AttributeError):
            pass  # Keep as original if conversion fails

        # Enhanced status information
        status = req['status']
        req['available'] = status == 'completed'
        req['can_delete'] = status == 'rejected'  # Only rejected requests can be deleted
        req['can_download'] = status == 'completed'

        # Add detailed status descriptions
        if status == 'pending_finance':
            req['status_description'] = 'Waiting for finance verification'
            req['status_color'] = 'warning'
            req['status_icon'] = '13.png'
        elif status == 'pending_confirmation':
            req['status_description'] = 'Pending finance confirmation'
            req['status_color'] = 'warning'
            req['status_icon'] = '13.png'
        elif status == 'approved_finance':
            req['status_description'] = 'Approved by finance, sent to faculty'
            req['status_color'] = 'info'
            req['status_icon'] = 'approved.png'
        elif status == 'faculty_processing':
            req['status_description'] = 'Faculty is preparing your transcript'
            req['status_color'] = 'info'
            req['status_icon'] = 'approved.png'
        elif status == 'completed':
            req['status_description'] = 'Transcript ready for download'
            req['status_color'] = 'success'
            req['status_icon'] = '8.png'
        elif status == 'done':
            req['status_description'] = 'Transcript downloaded'
            req['status_color'] = 'secondary'
            req['status_icon'] = 'done.png'
        elif status == 'rejected':
            rejection_reason = req.get('rejection_reason', 'Request rejected by finance')
            req['status_description'] = f'Rejected: {rejection_reason}'
            req['status_color'] = 'danger'
            req['status_icon'] = 'rejected.png'
        else:
            req['status_description'] = 'Processing your request'
            req['status_color'] = 'secondary'
            req['status_icon'] = '13.png'

        # Format dates for better display
        if req.get('rejected_at'):
            req['rejection_date'] = req['rejected_at']
        if req.get('approved_date'):
            req['approval_date'] = req['approved_date']

    # Get accurate request counts
    # Use finance dashboard logic for pending count
    pending_count = 0
    for req in requests:
        status = req['status']
        confirmed = req.get('finance_confirmed_at')

        if status == 'pending_finance':
            pending_count += 1
        elif status == 'pending_confirmation':
            if not confirmed:
                pending_count += 1  # Only count unconfirmed pending_confirmation requests
    approved_count = sum(1 for req in requests if req['status'] in ['approved_finance', 'faculty_processing'])
    completed_count = sum(1 for req in requests if req.get('completed_at') or req.get('transcript_file'))
    rejected_count = sum(1 for req in requests if req['status'] == 'rejected')
    requested_count = len(requests)

    # Get language from session, default to English
    language = session.get('language', 'en')
    current_translations = translations[language]

    return render_template('student/request_status.html',
                          requests=requests,
                          pending_count=pending_count,
                          approved_count=approved_count,
                          completed_count=completed_count,
                          rejected_count=rejected_count,
                          requested_count=requested_count,
                          translations=current_translations,
                          current_language=language,
                          get_current_language=get_current_language,
                          get_translation=get_translation)

@app.route('/view_downloads')
def view_downloads():
    """View downloads page with approved requests ready for download"""
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    student_reg_no = session['user_id']
    print(f"Getting approved requests for student: {student_reg_no}")

    # Get all requests for the student
    all_requests = get_requests_by_student_id(student_reg_no)
    print(f"Found {len(all_requests)} total requests")

    # Filter for completed requests only (transcript uploaded by faculty)
    completed_requests = []
    for req in all_requests:
        if req['status'] in ['completed', 'done']:
            # Add download tracking information - ensure download_count is properly handled
            download_count = req.get('download_count')
            if download_count is None:
                download_count = 0
            req['download_count'] = download_count
            req['last_downloaded'] = req.get('last_downloaded')
            req['approved_date'] = req.get('approved_date', req['date'])
            req['completed_date'] = req.get('completed_at', req.get('faculty_processed_at', req['date']))
            completed_requests.append(req)
            print(f"Request {req['id']}: status={req['status']}, download_count={download_count}")

    print(f"Found {len(completed_requests)} completed requests ready for download")

    # Calculate counts for the base template
    # Use finance dashboard logic for pending count
    pending_count = 0
    for req in all_requests:
        status = req['status']
        confirmed = req.get('finance_confirmed_at')

        if status == 'pending_finance':
            pending_count += 1
        elif status == 'pending_confirmation':
            if not confirmed:
                pending_count += 1  # Only count unconfirmed pending_confirmation requests
    approved_count = sum(1 for req in all_requests if req['status'] in ['approved_finance', 'faculty_processing'])
    completed_count = len(completed_requests)
    rejected_count = sum(1 for req in all_requests if req['status'] == 'rejected')
    requested_count = len(all_requests)  # Total requests

    # Get language from session, default to English
    language = session.get('language', 'en')
    current_translations = translations[language]

    return render_template('student/view_downloads.html',
                         approved_requests=completed_requests,  # Pass completed requests as approved_requests for template compatibility
                         pending_count=pending_count,
                         approved_count=approved_count,
                         completed_count=completed_count,
                         rejected_count=rejected_count,
                         requested_count=requested_count,
                         translations=current_translations,
                         current_language=language,
                         get_current_language=get_current_language,
                         get_translation=get_translation)

@app.route('/download_transcript/<request_id>')
def download_transcript(request_id):
    """Download transcript with support for multiple files"""
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    student_reg_no = session['user_id']

    # Get all student requests to find the specific one
    all_requests = get_requests_by_student_id(student_reg_no)
    request_data = None

    for req in all_requests:
        if str(req['id']) == str(request_id):
            request_data = req
            break

    if not request_data:
        flash('Request not found or you do not have permission to download this transcript.', 'error')
        return redirect(url_for('view_downloads'))

    # Check if request is completed (use payment_status from our actual database)
    payment_status = request_data.get('payment_status', request_data.get('status', 'pending'))
    if payment_status != 'paid':
        flash('This transcript is not yet ready for download.', 'error')
        return redirect(url_for('view_downloads'))

    # Check if already downloaded (use last_downloaded from our actual database)
    last_downloaded = request_data.get('last_downloaded')
    if last_downloaded:
        flash('This transcript has already been downloaded.', 'warning')
        return redirect(url_for('view_downloads'))

    # Get transcript files from database - check both files table and payment_proof_filename
    try:
        from simple_database_service import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # First check files table for transcript files
            cursor.execute("""
                SELECT filename, file_path FROM files
                WHERE request_id = %s AND file_type LIKE %s
            """, (request_id, 'transcript%'))
            file_results = cursor.fetchall()
            
            # Collect all possible filenames from files table
            filenames = []
            if file_results:
                for file_row in file_results:
                    filenames.append(file_row['filename'])
            else:
                # Single file default naming
                filenames = [f"transcript_{str(request_id)}.pdf"]
            
            # Check if files exist
            existing_files = []
            for filename in filenames:
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                if os.path.exists(file_path):
                    existing_files.append((filename, file_path))
            
            if not existing_files:
                flash('Transcript files not found. Please contact administration.', 'error')
                return redirect(url_for('view_downloads'))
            
            # Update download status (use correct column names for new schema)
            cursor.execute("""
                UPDATE transcript_requests
                SET last_downloaded = NOW()
                WHERE id = %s
            """, (request_id,))
            
            if cursor.rowcount == 0:
                flash('This transcript has already been downloaded.', 'warning')
                return redirect(url_for('view_downloads'))
            
            conn.commit()
            
            # If single file, send directly
            if len(existing_files) == 1:
                filename, file_path = existing_files[0]
                student_name_safe = str(request_data.get('student_name', 'Student')).replace(' ', '_')
                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=f"INES_Transcript_{student_name_safe}_{str(request_id)}.pdf",
                    mimetype='application/pdf'
                )
            
            # Multiple files - send first file and create separate download links
            filename, file_path = existing_files[0]
            student_name_safe = str(request_data.get('student_name', 'Student')).replace(' ', '_')
            return send_file(
                file_path,
                as_attachment=True,
                download_name=f"INES_Transcript_{student_name_safe}_{str(request_id)}_1.pdf",
                mimetype='application/pdf'
            )
                
    except Exception as e:
        print(f"Error during download: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        flash('Error downloading transcript. Please try again.', 'error')
        return redirect(url_for('view_downloads'))

@app.route('/test_email')
def test_email():
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    try:
        # Debug logging for email configuration
        print("\nEmail Configuration Check:")
        print(f"EMAIL_HOST: {EMAIL_HOST}")
        print(f"EMAIL_PORT: {EMAIL_PORT}")
        print(f"EMAIL_USERNAME: {EMAIL_USERNAME}")
        print(f"EMAIL_FROM: {EMAIL_FROM}")

        template_data = {
            'content': f"""
                <h2>Test Email</h2>
                <p>Dear {session['name']},</p>
                <p>This is a test email from the INES-Ruhengeri Transcript System.</p>
                <p>If you're receiving this, the email system is working correctly!</p>

                <h3>Test Details:</h3>
                <ul>
                    <li><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                    <li><strong>User:</strong> {session['name']}</li>
                    <li><strong>Email:</strong> {session['email']}</li>
                </ul>

                <p>Best regards,<br>
                INES-Ruhengeri Transcript System</p>
            """
        }

        if send_email_notification(session['email'], 'Test Email - INES-Ruhengeri Transcript System', template_data):
            flash('Test email sent successfully! Please check your inbox.', 'success')
        else:
            flash('Failed to send test email. Please check the server logs.', 'error')

    except Exception as e:
        error_msg = f'Error sending test email: {str(e)}'
        print(error_msg)
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        flash(error_msg, 'error')

    return redirect(url_for('student_dashboard'))

@app.route('/debug/download-status/<int:request_id>')
def debug_download_status(request_id):
    """Debug route to check download status"""
    if session.get('role') != 'student':
        return redirect(url_for('login_page'))

    try:
        from new_database_service import get_db_connection
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT tr.id, tr.payment_status as status, tr.last_downloaded,
                           u.reg_no as student_id, u.name as student_name
                    FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s AND u.reg_no = %s
                """, (request_id, session.get('user_id')))

                result = cursor.fetchone()
                if result:
                    debug_info = {
                        'request_id': result[0],
                        'status': result[1],
                        'download_count': result[2],
                        'last_downloaded': result[3],
                        'student_id': result[4],
                        'student_name': result[5]
                    }
                    return f"<pre>Debug Info for Request {request_id}:\n{json.dumps(debug_info, indent=2, default=str)}</pre>"
                else:
                    return f"Request {request_id} not found for student {session.get('user_id')}"
    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/debug/test-finance-notification')
def test_finance_notification():
    """Debug route to test finance email notifications"""
    if session.get('role') not in ['finance', 'admin']:
        return "Access denied. Only finance/admin users can test notifications."

    try:
        # Create a test request data
        test_request = {
            'id': 'TEST001',
            'student_name': 'Test Student',
            'student_id': 'STU001',
            'department': 'Computer Science',
            'academic_years': ['2023-2024'],
            'total_price': 5000,
            'payment_method': 'mobile_money',
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        print("🧪 Testing finance notification system...")
        notify_finance_staff(test_request)

        return f"""
        <h2>Finance Notification Test</h2>
        <p>Test notification sent for request: {test_request['id']}</p>
        <p>Check the console logs for detailed information.</p>
        <p>Check finance email inboxes for the test notification.</p>
        <hr>
        <pre>{json.dumps(test_request, indent=2)}</pre>
        """
    except Exception as e:
        return f"Error testing finance notification: {str(e)}"

@app.route('/debug/finance-users')
def debug_finance_users():
    """Debug route to check finance users in database"""
    if session.get('role') not in ['finance', 'admin']:
        return "Access denied. Only finance/admin users can view this information."

    try:
        from simple_database_service import get_db_connection
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                # Get all users with finance role
                cursor.execute("""
                    SELECT name, email, reg_no, role, is_active, created_at
                    FROM users
                    WHERE role LIKE '%finance%' OR role = 'admin'
                    ORDER BY role, name
                """)

                users = cursor.fetchall()

                result = "<h2>Finance Users in Database</h2>"
                if users:
                    result += "<table border='1' style='border-collapse: collapse; width: 100%;'>"
                    result += "<tr><th>Name</th><th>Email</th><th>Reg No</th><th>Role</th><th>Active</th><th>Created</th></tr>"
                    for user in users:
                        result += f"<tr><td>{user[0]}</td><td>{user[1]}</td><td>{user[2]}</td><td>{user[3]}</td><td>{'Yes' if user[4] else 'No'}</td><td>{user[5]}</td></tr>"
                    result += "</table>"
                else:
                    result += "<p>No finance users found in database.</p>"

                # Also test email retrieval
                finance_emails = get_department_emails('finance')
                result += f"<h3>Finance Emails Retrieved: {len(finance_emails)}</h3>"
                result += f"<ul>{''.join([f'<li>{email}</li>' for email in finance_emails])}</ul>"

                return result

    except Exception as e:
        return f"Error checking finance users: {str(e)}"

@app.route('/student/delete-request/<int:request_id>', methods=['POST'])
def student_delete_request(request_id):
    """Allow students to delete their own requests"""
    if session.get('role') != 'student':
        flash('Unauthorized access', 'error')
        return redirect(url_for('login_page'))

    student_id = session.get('user_id')

    try:
        # Use MySQL database to find and delete the request
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Find the request and verify it belongs to the student
                cursor.execute("""
                    SELECT tr.*, u.id as user_id FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s AND u.reg_no = %s
                """, (request_id, student_id))

                request_to_delete = cursor.fetchone()

                if request_to_delete:
                    # Check if request can be deleted (allow deletion of pending, rejected, and done requests)
                    if request_to_delete['status'] == 'rejected':
                        # Delete the request
                        cursor.execute("""
                            DELETE FROM transcript_requests WHERE id = %s
                        """, (request_id,))
                        connection.commit()

                        # Invalidate cache for this student (if Redis available)
                        if REDIS_AVAILABLE:
                            redis_service = get_redis_service()
                            if redis_service:
                                redis_service.invalidate_student_cache(student_id)

                        # Flash success message
                        flash(f'Request #{request_id} has been deleted successfully', 'success')
                    else:
                        flash('Only rejected requests can be deleted', 'error')
                else:
                    flash('Request not found or you do not have permission to delete it', 'error')

    except Exception as e:
        flash(f'Error deleting request: {str(e)}', 'error')
        import traceback
        print(f"Error deleting request: {traceback.format_exc()}")

    return redirect(url_for('request_status'))

@app.route('/student/delete-downloaded-request/<int:request_id>', methods=['POST'])
def student_delete_downloaded_request(request_id):
    """Allow students to delete downloaded transcript requests from their view downloads list"""
    if session.get('role') != 'student':
        flash('Unauthorized access', 'error')
        return redirect(url_for('login_page'))

    student_id = session.get('user_id')

    try:
        # Use MySQL database to find and delete the downloaded request
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Find the request and verify it belongs to the student and is downloaded
                cursor.execute("""
                    SELECT tr.*, u.id as user_id FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s AND u.reg_no = %s
                """, (request_id, student_id))

                request_to_delete = cursor.fetchone()

                if request_to_delete:
                    # Only allow deletion of downloaded requests (status = 'done')
                    if request_to_delete['status'] == 'done':
                        # Also delete related files if they exist
                        cursor.execute("""
                            DELETE FROM files WHERE request_id = %s
                        """, (request_id,))

                        # Delete the main request
                        cursor.execute("""
                            DELETE FROM transcript_requests WHERE id = %s
                        """, (request_id,))

                        connection.commit()

                        # Invalidate cache for this student (if Redis available)
                        if REDIS_AVAILABLE:
                            redis_service = get_redis_service()
                            if redis_service:
                                redis_service.invalidate_student_cache(student_id)

                        # Flash success message
                        flash(f'✅ Downloaded transcript #{request_id} has been removed from your list successfully', 'success')
                    else:
                        flash('❌ Only downloaded transcripts can be deleted from this page', 'error')
                else:
                    flash('❌ Request not found or you do not have permission to delete it', 'error')

    except Exception as e:
        flash(f'❌ Error deleting downloaded request: {str(e)}', 'error')
        import traceback
        print(f"Error deleting downloaded request: {traceback.format_exc()}")

    return redirect(url_for('view_downloads'))

@app.route('/student/clear-all-requests', methods=['POST'])
def student_clear_all_requests():
    """Allow students to clear all their deletable requests"""
    if session.get('role') != 'student':
        flash('Unauthorized access', 'error')
        return redirect(url_for('login_page'))

    student_id = session.get('user_id')
    deleted_count = 0
    skipped_count = 0

    try:
        # Use MySQL database to find and delete all deletable requests for this student
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Find all requests for this student
                cursor.execute("""
                    SELECT tr.*, u.id as user_id FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE u.reg_no = %s
                """, (student_id,))

                all_requests = cursor.fetchall()

                # Delete only approved, rejected, and downloadable requests (keep pending as reminders)
                for request in all_requests:
                    if request['status'] in ['approved_finance', 'completed', 'rejected', 'done']:
                        cursor.execute("""
                            DELETE FROM transcript_requests WHERE id = %s
                        """, (request['id'],))
                        deleted_count += 1
                    else:
                        skipped_count += 1

                connection.commit()

                # Invalidate cache for this student (if Redis available)
                if REDIS_AVAILABLE:
                    redis_service = get_redis_service()
                    if redis_service:
                        redis_service.invalidate_student_cache(student_id)

                # Flash appropriate message
                if deleted_count > 0:
                    if skipped_count > 0:
                        flash(f'✅ Cleared {deleted_count} completed requests successfully. {skipped_count} pending requests were kept as reminders.', 'success')
                    else:
                        flash(f'✅ All {deleted_count} completed requests have been cleared successfully.', 'success')
                else:
                    if skipped_count > 0:
                        flash('ℹ️ No requests were deleted. All your requests are currently pending (kept as reminders).', 'info')
                    else:
                        flash('ℹ️ No requests found to delete.', 'info')

    except Exception as e:
        flash(f'Error clearing requests: {str(e)}', 'error')
        import traceback
        print(f"Error clearing all requests: {traceback.format_exc()}")

    return redirect(url_for('student_dashboard'))

@app.route('/finance/clear-all-requests', methods=['POST'])
@login_required
@finance_required
def finance_clear_all_requests():
    """Allow finance to clear all processed requests"""
    deleted_count = 0
    skipped_count = 0

    try:
        # Use MySQL database to find and delete all processed requests
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Find all requests
                cursor.execute("SELECT * FROM transcript_requests")
                all_requests = cursor.fetchall()

                # Delete only processed requests (keep pending for processing)
                for request in all_requests:
                    if request['status'] in ['approved_finance', 'completed', 'rejected', 'done', 'pending_confirmation']:
                        # Delete related data first
                        cursor.execute("DELETE FROM files WHERE request_id = %s", (request['id'],))

                        # Delete the request
                        cursor.execute("DELETE FROM transcript_requests WHERE id = %s", (request['id'],))
                        deleted_count += 1
                    else:
                        skipped_count += 1

                connection.commit()

                # Flash appropriate message
                if deleted_count > 0:
                    if skipped_count > 0:
                        flash(f'✅ Cleared {deleted_count} processed requests successfully. {skipped_count} pending requests were kept for processing.', 'success')
                    else:
                        flash(f'✅ All {deleted_count} processed requests have been cleared successfully.', 'success')
                else:
                    if skipped_count > 0:
                        flash('ℹ️ No requests were deleted. All requests are currently pending processing.', 'info')
                    else:
                        flash('ℹ️ No requests found to delete.', 'info')

    except Exception as e:
        flash(f'Error clearing requests: {str(e)}', 'error')
        import traceback
        print(f"Error clearing all finance requests: {traceback.format_exc()}")

    return redirect(url_for('finance_dashboard'))

@app.route('/faculty/clear-all-requests', methods=['POST'])
@login_required
@faculty_required
def faculty_clear_all_requests():
    """Allow faculty to clear all completed requests"""
    deleted_count = 0
    skipped_count = 0

    try:
        # Use MySQL database to find and delete all completed requests
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Find all requests
                cursor.execute("SELECT * FROM transcript_requests")
                all_requests = cursor.fetchall()

                # Delete only completed requests (keep pending for processing)
                for request in all_requests:
                    if request['status'] in ['completed', 'done']:
                        # Delete related data first
                        cursor.execute("DELETE FROM files WHERE request_id = %s", (request['id'],))

                        # Delete the request
                        cursor.execute("DELETE FROM transcript_requests WHERE id = %s", (request['id'],))
                        deleted_count += 1
                    else:
                        skipped_count += 1

                connection.commit()

                # Flash appropriate message
                if deleted_count > 0:
                    if skipped_count > 0:
                        flash(f'✅ Cleared {deleted_count} completed uploads successfully. {skipped_count} pending/processing requests were kept.', 'success')
                    else:
                        flash(f'✅ All {deleted_count} completed uploads have been cleared successfully.', 'success')
                else:
                    if skipped_count > 0:
                        flash('ℹ️ No requests were deleted. All requests are currently pending or being processed.', 'info')
                    else:
                        flash('ℹ️ No requests found to delete.', 'info')

    except Exception as e:
        flash(f'Error clearing requests: {str(e)}', 'error')
        import traceback
        print(f"Error clearing all faculty requests: {traceback.format_exc()}")

    return redirect(url_for('faculty_dashboard'))

@app.route('/faculty/delete-request/<int:request_id>', methods=['POST'])
@login_required
@faculty_required
def faculty_delete_request_from_history(request_id):
    """Delete a completed request from faculty history"""
    try:
        # Use the database service delete function
        if delete_transcript_request(request_id, session.get('user_id')):
            flash(f'Request {request_id} has been deleted successfully', 'success')
        else:
            flash('Error deleting request or request not found', 'error')
    except Exception as e:
        flash(f'Error deleting request: {str(e)}', 'error')

    return redirect(url_for('request_history'))

# Finance routes
@app.route('/finance/dashboard')
@login_required
def finance_dashboard():
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    # Get finance dashboard data from simple database service
    dashboard_data = get_finance_dashboard_data()

    return render_template('finance/enhanced_dashboard_new.html',
                         pending_requests=dashboard_data['pending_requests'],
                         approved_requests=dashboard_data['approved_requests'],
                         rejected_requests=dashboard_data['rejected_requests'],
                         pending_count=dashboard_data['pending_count'],
                         approved_count=dashboard_data['approved_count'],
                         rejected_count=dashboard_data['rejected_count'],
                         total_count=dashboard_data['total_count'])

@app.route('/finance/approve-request', methods=['POST'])
@login_required
def finance_approve_request():
    """Approve a transcript request"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    request_id = request.form.get('request_id')
    if not request_id:
        flash('Invalid request ID.', 'error')
        return redirect(url_for('view_status'))

    try:
        # Get finance user ID from session
        finance_user_reg_no = session.get('user_id')
        print(f"🔍 Finance approval attempt by user: {finance_user_reg_no} for request: {request_id}")

        if approve_transcript_request(request_id, finance_user_reg_no):
            flash('Request approved successfully! Student and faculty have been notified.', 'success')
            print(f"✅ Request {request_id} approved and notifications sent")
        else:
            flash('Error approving request. Please try again.', 'error')
            print(f"❌ Failed to approve request {request_id}")

    except Exception as e:
        print(f"❌ Exception in finance_approve_request: {e}")
        flash(f'Error approving request: {str(e)}', 'error')

    return redirect(url_for('view_status'))

@app.route('/finance/reject-request', methods=['POST'])
@login_required
def finance_reject_request():
    """Reject a transcript request"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    request_id = request.form.get('request_id')
    if not request_id:
        flash('Invalid request ID.', 'error')
        return redirect(url_for('view_status'))

    try:
        # Get finance user ID from session
        finance_user_reg_no = session.get('user_id')
        custom_reason = request.form.get('rejection_reason')  # Allow custom reason from form

        print(f"🔍 Finance rejection attempt by user: {finance_user_reg_no} for request: {request_id}")
        if custom_reason:
            print(f"📝 Custom rejection reason provided: {custom_reason}")

        if reject_transcript_request(request_id, finance_user_reg_no, custom_reason):
            flash('Request rejected successfully! Student has been notified.', 'success')
            print(f"✅ Request {request_id} rejected and student notified")
        else:
            flash('Error rejecting request. Please try again.', 'error')
            print(f"❌ Failed to reject request {request_id}")

    except Exception as e:
        print(f"❌ Exception in finance_reject_request: {e}")
        flash(f'Error rejecting request: {str(e)}', 'error')

    return redirect(url_for('view_status'))

@app.route('/finance/delete-request', methods=['POST'])
@login_required
def finance_delete_request():
    """Delete a transcript request (approved or rejected)"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    request_id = request.form.get('request_id')
    if not request_id:
        flash('Invalid request ID.', 'error')
        return redirect(url_for('view_history'))

    try:
        # Get finance user ID from session
        finance_user_reg_no = session.get('user_id')

        print(f"🔍 Finance deletion attempt by user: {finance_user_reg_no} for request: {request_id}")

        if delete_transcript_request(request_id, finance_user_reg_no):
            flash('Request deleted successfully!', 'success')
        else:
            flash('Error deleting request. Please try again.', 'error')

    except Exception as e:
        print(f"❌ Exception in finance_delete_request: {e}")
        flash(f'Error deleting request: {str(e)}', 'error')

    return redirect(url_for('view_history'))

@app.route('/view-payment-proof/<filename>')
@login_required
def view_payment_proof(filename):
    """View payment proof file"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    try:
        import os
        proof_path = os.path.join(app.config['UPLOAD_FOLDER'], 'payment_proofs', filename)

        if os.path.exists(proof_path):
            return send_file(proof_path)
        else:
            flash('Payment proof file not found.', 'error')
            return redirect(url_for('finance_dashboard'))

    except Exception as e:
        flash(f'Error viewing payment proof: {str(e)}', 'error')
        return redirect(url_for('finance_dashboard'))

@app.route('/finance/view-status')
@login_required
def view_status():
    """Finance view status page - shows all pending requests"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    # Get finance dashboard data
    dashboard_data = get_finance_dashboard_data()

    return render_template('finance/view_status_new.html',
                         pending_requests=dashboard_data['pending_requests'])



@app.route('/finance/view-history')
@login_required
def view_history():
    """Finance view history page - shows approved and rejected requests"""
    if session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    # Get finance dashboard data
    dashboard_data = get_finance_dashboard_data()

    return render_template('finance/view_history_new.html',
                         approved_requests=dashboard_data['approved_requests'],
                         rejected_requests=dashboard_data['rejected_requests'],
                         approved_count=len(dashboard_data['approved_requests']),
                         rejected_count=len(dashboard_data['rejected_requests']))

@app.route('/finance/delete-request/<int:request_id>', methods=['POST'])
@login_required
@finance_required
def delete_request_from_history(request_id):
    """Delete a request permanently from the system"""
    if delete_transcript_request(request_id, session.get('user_id')):
        flash(f'Request {request_id} has been deleted successfully', 'success')
    else:
        flash('Error deleting request', 'error')

    return redirect(url_for('view_history'))

@app.route('/finance/confirm-request/<int:request_id>', methods=['POST'])
@login_required
@finance_required
def confirm_request(request_id):
    """Confirm a request and send it to faculty or back to student"""
    try:
        # Get request details from pending confirmations (not finance history)
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.reg_no as student_id, u.email,
                           u.department_name as department, u.faculty_name as faculty, u.reg_no as student_user_id
                    FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s AND tr.payment_status = 'pending'
                """, (request_id,))

                request_row = cursor.fetchone()

                if not request_row:
                    flash('Request not found or not in pending confirmation status', 'error')
                    return redirect(url_for('view_status'))

                # Convert to expected format
                request_data = {
                    'id': request_row['id'],
                    'student_id': request_row['student_id'],
                    'student_name': request_row['student_name'],
                    'student_user_id': request_row['student_user_id'],
                    'email': request_row['email'],
                    'department': request_row['department'],
                    'faculty': request_row['faculty'],
                    'academic_years': json.loads(request_row['academic_years']),
                    'status': request_row['status'],
                    'auto_decision': request_row['auto_decision'],
                    'auto_decision_reason': request_row['auto_decision_reason'],
                    'finance_confirmed_at': request_row['finance_confirmed_at']
                }

        # Check if already confirmed
        if request_data.get('finance_confirmed_at'):
            flash(f'Request {request_id} has already been confirmed', 'warning')
            return redirect(url_for('view_status'))

        # Process the confirmation (request is already verified to be pending_confirmation)
        if request_data['status'] == 'pending_confirmation':
            auto_decision = request_data.get('auto_decision')

            with get_db_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:

                    if auto_decision == 'approve':
                        # Confirm approval and send to faculty
                        # Get the finance user reg_no
                        finance_user_reg_no = session.get('user_id')  # This is reg_no

                        cursor.execute("""
                            UPDATE transcript_requests
                            SET status = 'approved_finance',
                                finance_confirmed_at = %s,
                                finance_confirmed_by = %s,
                                finance_approved_at = %s,
                                finance_approved_by = %s
                            WHERE id = %s AND finance_confirmed_at IS NULL
                        """, (datetime.now(), finance_user_reg_no, datetime.now(), finance_user_reg_no, request_id))

                        if cursor.rowcount == 0:
                            flash(f'Request {request_id} has already been confirmed', 'warning')
                            return redirect(url_for('view_status'))

                        connection.commit()

                        # Clear student cache to ensure they see the approval immediately
                        if REDIS_AVAILABLE:
                            redis_service = get_redis_service()
                            if redis_service:
                                # Get student ID from request data
                                student_user_id = request_data.get('student_user_id')
                                if student_user_id:
                                    redis_service.invalidate_student_cache(student_user_id)

                        # Notify student about approval
                        notify_student_approval(request_data)

                        # Notify faculty staff
                        notify_faculty_staff(request_data)
                        flash(f'Request {request_id} confirmed and sent to faculty', 'success')

                    elif auto_decision == 'reject':
                        # Get rejection reason from form or use auto-decision reason
                        rejection_reason = request.form.get('rejection_reason', request_data.get('auto_decision_reason', 'Request rejected by finance'))

                        # Confirm rejection and notify student
                        # Get the finance user reg_no
                        finance_user_reg_no = session.get('user_id')  # This is reg_no

                        cursor.execute("""
                            UPDATE transcript_requests
                            SET status = 'rejected',
                                finance_confirmed_at = %s,
                                finance_confirmed_by = %s,
                                rejected_at = %s,
                                rejected_by = %s,
                                rejection_reason = %s
                            WHERE id = %s AND finance_confirmed_at IS NULL
                        """, (datetime.now(), finance_user_reg_no, datetime.now(), finance_user_reg_no, rejection_reason, request_id))

                        if cursor.rowcount == 0:
                            flash(f'Request {request_id} has already been confirmed', 'warning')
                            return redirect(url_for('view_status'))

                        connection.commit()

                        # Clear student cache to ensure they see the rejection immediately
                        if REDIS_AVAILABLE:
                            redis_service = get_redis_service()
                            if redis_service:
                                # Get student ID from request data
                                student_user_id = request_data.get('student_user_id')
                                if student_user_id:
                                    redis_service.invalidate_student_cache(student_user_id)

                        # Notify student about rejection
                        notify_student_rejection(request_data, rejection_reason)

                        flash(f'Request {request_id} rejection confirmed and sent back to student panel', 'success')
                    else:
                        flash(f'Invalid auto-decision for request {request_id}', 'error')
                        return redirect(url_for('view_status'))
        else:
            flash(f'Request {request_id} is not in pending confirmation status (current: {request_data["status"]})', 'error')
            return redirect(url_for('view_status'))



        return redirect(url_for('view_status'))

    except Exception as e:
        flash(f'Error confirming request: {str(e)}', 'error')
        return redirect(url_for('view_status'))





@app.route('/finance/delete-request/<request_id>', methods=['POST'])
@login_required
@finance_required
def delete_request(request_id):
    """Delete a request from MySQL database"""
    try:
        # Use the database service delete function
        if delete_transcript_request(int(request_id), session.get('user_id')):
            flash(f'Request {request_id} has been deleted successfully', 'success')
        else:
            flash('Error deleting request or request not found', 'error')
    except Exception as e:
        flash(f'Error deleting request: {str(e)}', 'error')

    return redirect(url_for('view_history'))

# Faculty routes
@app.route('/faculty/dashboard')
def faculty_dashboard():
    if session.get('role') != 'faculty':
        return redirect(url_for('login_page'))

    faculty_department = session.get('department')
    print(f"Faculty dashboard - Faculty: {faculty_department}")
    
    # Get approved requests filtered by faculty
    all_pending = get_pending_requests_for_faculty()
    all_completed = get_completed_requests_for_faculty()
    
    # Filter by faculty department - use consistent mapping
    def belongs_to_faculty(request_dept, faculty_name):
        print(f"Dashboard: Checking if department '{request_dept}' belongs to faculty '{faculty_name}'")
        
        # Use the same mapping as upload_transcript for consistency
        faculty_dept_map = {
            'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
            'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
            'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
            'Faculty of Education': ['French and English'],
            'Faculty of Law': ['Law']
        }
        
        result = request_dept in faculty_dept_map.get(faculty_name, [])
        print(f"Dashboard: Department '{request_dept}' belongs to faculty '{faculty_name}': {result}")
        return result
    
    print(f"Total pending requests: {len(all_pending)}")
    pending_requests = [req for req in all_pending if belongs_to_faculty(req.get('department', ''), faculty_department)]
    print(f"Filtered pending for {faculty_department}: {len(pending_requests)}")
    completed_requests = [req for req in all_completed if belongs_to_faculty(req.get('department', ''), faculty_department)]

    pending_count = len(pending_requests)
    completed_count = len(completed_requests)
    
    print(f"Showing {pending_count} pending requests for {faculty_department}")

    return render_template('faculty/dashboard.html',
                          pending_count=pending_count,
                          completed_count=completed_count,
                          requests=pending_requests,
                          completed_requests=completed_requests)

@app.route('/faculty/upload-transcript', methods=['GET', 'POST'])
def upload_transcript():
    if session.get('role') != 'faculty':
        return redirect(url_for('login_page'))
    
    faculty_department = session.get('department')
    
    if request.method == 'POST':
        request_id = request.form['request_id']
        print(f"🔍 Looking for request ID: {request_id}")
        request_data = get_request_by_id(request_id)
        print(f"📋 Found request data: {request_data is not None}")
        if request_data:
            print(f"🎯 Request details: ID={request_data.get('id')}, Student={request_data.get('student_name')}, Dept={request_data.get('department')}")
        
        # Check how many academic years are requested
        academic_years = request_data.get('academic_years', []) if request_data else []
        years_count = len(academic_years)
        
        print(f"Request {request_id} has {years_count} academic years: {academic_years}")
        
        # Handle multiple file uploads based on years count
        uploaded_files = []
        
        if years_count == 1:
            # Single transcript upload
            if 'transcript' not in request.files:
                flash('Please upload the transcript file', 'error')
                return redirect(request.url)
            
            file = request.files['transcript']
            if file.filename == '':
                flash('No file selected', 'error')
                return redirect(request.url)
            
            uploaded_files.append((file, academic_years[0]))
        else:
            # Multiple transcript uploads - one file per academic year
            for i in range(years_count):
                file_key = f'transcript_{i+1}'
                if file_key not in request.files:
                    flash(f'Please upload transcript for {academic_years[i]}', 'error')
                    return redirect(request.url)
                
                file = request.files[file_key]
                if file.filename == '':
                    flash(f'No file selected for {academic_years[i]}', 'error')
                    return redirect(request.url)
                
                uploaded_files.append((file, academic_years[i]))
        
        if not request_data:
            flash('Request not found', 'error')
            return redirect(url_for('upload_transcript'))
        
        # Check if request belongs to faculty's department
        def belongs_to_faculty(request_dept, faculty_name):
            print(f"🔍 BELONGS_TO_FACULTY CHECK:")
            print(f"   Request department: '{request_dept}'")
            print(f"   Faculty name: '{faculty_name}'")
            
            # Use the hardcoded mapping that works with the current system
            faculty_dept_map = {
                'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
                'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
                'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
                'Faculty of Education': ['French and English'],
                'Faculty of Law': ['Law']
            }
            
            available_depts = faculty_dept_map.get(faculty_name, [])
            print(f"   Available departments for this faculty: {available_depts}")
            
            result = request_dept in available_depts
            print(f"   Result: {result}")
            return result
        
        print(f"🔍 DETAILED DEBUG:")
        print(f"   Request ID: {request_id}")
        print(f"   Request data exists: {request_data is not None}")
        if request_data:
            print(f"   Request department: '{request_data.get('department', 'None')}'")
            print(f"   Faculty department: '{faculty_department}'")
            print(f"   Request status: '{request_data.get('status', 'None')}'")
            print(f"   Student name: '{request_data.get('student_name', 'None')}'")
        
        if not belongs_to_faculty(request_data.get('department', ''), faculty_department):
            print(f"❌ FACULTY CHECK FAILED:")
            print(f"   Request department: '{request_data.get('department', '')}'")
            print(f"   Faculty department: '{faculty_department}'")
            flash(f'This request is not from your faculty department. Request dept: "{request_data.get("department", "Unknown")}" vs Faculty: "{faculty_department}"', 'error')
            return redirect(url_for('upload_transcript'))
        
        print(f"✅ FACULTY CHECK PASSED - PROCEEDING WITH UPLOAD:")
        print(f"👨‍🏫 Faculty {session['name']} processing request {request_id} for department {request_data.get('department')}")
        print(f"🏢 Faculty department: {faculty_department}")
        print(f"📁 Years count: {years_count}, Academic years: {academic_years}")
        
        # Save all uploaded files with proper naming
        saved_filenames = []
        for file, year in uploaded_files:
            if years_count == 1:
                filename = f"transcript_{request_id}.pdf"
            else:
                filename = f"transcript_{request_id}_{year.replace('-', '_')}.pdf"
            
            file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
            saved_filenames.append(filename)
            print(f"Saved file: {filename} for year {year}")

            print(f"🔍 DEBUG: File saved as {filename}")
            print(f"🔍 DEBUG: Request data: {request_data}")

            if request_data:
                print(f"🔍 DEBUG: Request data keys: {list(request_data.keys())}")
                print(f"🔍 DEBUG: Student ID: {request_data.get('student_id')}")
                print(f"🔍 DEBUG: Student Name: {request_data.get('student_name')}")
                print(f"🔍 DEBUG: Academic Years: {request_data.get('academic_years')}")
            else:
                print("❌ DEBUG: request_data is None or empty!")

            # Update database using simple database service
            from simple_database_service import add_transcript as db_add_transcript

            print(f"🔄 DEBUG: Calling db_add_transcript with:")
            print(f"   - request_id: {request_id}")
            print(f"   - student_reg_no: {request_data.get('student_id') if request_data else 'None'}")
            print(f"   - student_name: {request_data.get('student_name') if request_data else 'None'}")
            print(f"   - academic_years: {request_data.get('academic_years', []) if request_data else 'None'}")
            print(f"   - filename: {filename}")

            # Store individual filenames separately in database
            main_filename = saved_filenames[0] if saved_filenames else f"transcript_{request_id}.pdf"
            
            # Store additional files in files table
            if len(saved_filenames) > 1:
                try:
                    with get_db_connection() as conn:
                        cursor = conn.cursor()
                        for i, filename in enumerate(saved_filenames):
                            cursor.execute("""
                                INSERT INTO files (request_id, file_type, filename, file_path, uploaded_by)
                                VALUES (%s, %s, %s, %s, %s)
                            """, (request_id, f'transcript_{i+1}', filename, f"static/uploads/{filename}", session['user_id']))
                        conn.commit()
                except Exception as e:
                    print(f"Error storing additional files: {e}")
            
            # Debug the upload process
            print(f"Processing upload for request {request_id}")
            print(f"Request data: {request_data}")
            print(f"Main filename: {main_filename}")
            
            # Debug the upload process
            print(f"🔄 Processing upload for request {request_id}")
            print(f"📋 Request data exists: {request_data is not None}")
            print(f"📁 Main filename: {main_filename}")
            print(f"📂 Saved filenames: {saved_filenames}")
            
            success = db_add_transcript(
                request_id,
                request_data.get('student_id'),
                request_data.get('student_name'),
                request_data.get('academic_years', []),
                main_filename
            )
            
            if request_data and success:
                print("✅ DEBUG: db_add_transcript returned True")
                
                # Get student email for notification
                try:
                    from simple_database_service import get_db_connection
                    import pymysql
                    with get_db_connection() as connection:
                        with connection.cursor() as cursor:
                            cursor.execute("""
                                SELECT email FROM users
                                WHERE reg_no = %s
                            """, (request_data.get('student_id'),))
                            result = cursor.fetchone()
                            if result:
                                request_data['email'] = result[0]
                                print(f"✅ Found student email for notification: {result[0]}")
                            else:
                                print(f"❌ No email found for student {request_data.get('student_id')}")
                except Exception as e:
                    print(f"❌ Error getting student email: {e}")

                # Clear student cache to ensure they see the completion immediately
                if REDIS_AVAILABLE:
                    redis_service = get_redis_service()
                    if redis_service and request_data:
                        # Get student ID from request data
                        student_user_id = request_data.get('student_id')
                        if student_user_id:
                            redis_service.invalidate_student_cache(student_user_id)



                # Send completion notification
                print(f"📧 Sending completion notification for request {request_id}...")
                try:
                    notify_student_transcript_ready(request_data)
                    print(f"✅ Notification sent successfully")
                except Exception as e:
                    print(f"❌ Error sending notification: {e}")
                
                # Ensure request status is set to completed for student download
                print(f"✅ Request {request_id} marked as completed for student download")

                if years_count == 1:
                    flash('✅ Transcript uploaded successfully! Student has been notified.', 'success')
                else:
                    flash(f'✅ All {years_count} transcripts uploaded successfully! Student has been notified.', 'success')
                
                print(f"✅ UPLOAD SUCCESS: Request {request_id} completed successfully")

                # Redirect back to upload page to show updated list
                return redirect(url_for('upload_transcript'))
            else:
                print("❌ DEBUG: db_add_transcript returned False or request_data is None")
                if not request_data:
                    flash('❌ Error: Request not found. Please verify the request ID and try again.', 'error')
                else:
                    flash('❌ Error: Transcript upload failed. Please check the file format and try again.', 'error')

                # Stay on upload page to show error and allow retry
                return redirect(url_for('upload_transcript'))
    
    # Get approved requests filtered by faculty
    all_requests = get_pending_requests_for_faculty()
    
    def belongs_to_faculty(request_dept, faculty_name):
        print(f"GET: Checking if department '{request_dept}' belongs to faculty '{faculty_name}'")
        
        faculty_dept_map = {
            'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
            'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
            'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
            'Faculty of Education': ['French and English'],
            'Faculty of Law': ['Law']
        }
        
        result = request_dept in faculty_dept_map.get(faculty_name, [])
        print(f"GET: Department '{request_dept}' belongs to faculty '{faculty_name}': {result}")
        return result
    
    requests = [req for req in all_requests if belongs_to_faculty(req.get('department', ''), faculty_department)]
    
    pending_count = len(requests)
    
    print(f"Upload transcript page - showing {pending_count} approved requests")
    
    return render_template('faculty/upload_transcript.html', 
                          requests=requests,
                          pending_count=pending_count)



@app.route('/faculty/request-history')
@login_required
@faculty_required
def request_history():
    faculty_department = session.get('department')

    try:
        # Get all uploaded requests (completed and downloaded) from MySQL database
        all_requests = get_all_requests()  # From new_database_service
        

        
        # Define belongs_to_faculty function for request history
        def belongs_to_faculty(request_dept, faculty_name):
            print(f"History: Checking if department '{request_dept}' belongs to faculty '{faculty_name}'")
            
            faculty_dept_map = {
                'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
                'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
                'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
                'Faculty of Education': ['French and English'],
                'Faculty of Law': ['Law']
            }
            
            result = request_dept in faculty_dept_map.get(faculty_name, [])
            print(f"History: Department '{request_dept}' belongs to faculty '{faculty_name}': {result}")
            return result
        
        uploaded_requests = []
        for req in all_requests:
            if req['status'] in ['completed', 'done'] and belongs_to_faculty(req.get('department', ''), faculty_department):
                # Add additional formatting for display with real upload dates
                req['upload_date'] = req.get('faculty_processed_at') or req.get('completed_at') or req.get('created_at')
                req['download_date'] = req.get('downloaded_at') or req.get('last_downloaded')
                req['download_count'] = req.get('download_count', 0)

                # Add completion timestamp for display
                req['completed_at'] = req.get('faculty_processed_at') or req.get('completed_at') or req.get('created_at')
                
                # Format upload date for display
                if req['upload_date']:
                    if isinstance(req['upload_date'], str):
                        req['upload_date_formatted'] = req['upload_date']
                    else:
                        req['upload_date_formatted'] = req['upload_date'].strftime('%Y-%m-%d %H:%M:%S')

                # Ensure academic_years is properly formatted
                if isinstance(req.get('academic_years'), str):
                    try:
                        import json
                        req['academic_years'] = json.loads(req['academic_years'])
                    except:
                        req['academic_years'] = [req['academic_years']]
                elif not req.get('academic_years'):
                    req['academic_years'] = []

                uploaded_requests.append(req)

        # Sort by upload date (most recent first)
        uploaded_requests.sort(key=lambda x: x.get('faculty_processed_at', x.get('created_at', '')), reverse=True)

        # Get pending count for sidebar badges (faculty-specific) - exclude completed requests
        all_pending = get_pending_requests_for_faculty()
        pending_requests = [req for req in all_pending if belongs_to_faculty(req.get('department', ''), faculty_department) and req['status'] == 'approved_finance']
        print(f"Faculty {faculty_department} has {len(pending_requests)} pending requests")
        pending_count = len(pending_requests)

        return render_template('faculty/request_history.html',
                             requests=uploaded_requests,
                             total_uploaded=len(uploaded_requests),
                             pending_count=pending_count)
    except Exception as e:
        flash(f'Error loading request history: {str(e)}', 'error')
        return redirect(url_for('faculty_dashboard'))

@app.route('/clear-requests')
def clear_requests_route():
    """Admin route to clear all requests from MySQL database"""
    try:
        # Only allow admin access
        if session.get('role') != 'admin':
            flash('Unauthorized access - Admin only', 'error')
            return redirect(url_for('login_page'))

        # Clear all requests using the updated function
        clear_requests()
        flash('✅ All requests have been cleared from MySQL database. System ready for new requests.', 'success')

    except Exception as e:
        flash(f'❌ Error clearing requests: {str(e)}', 'error')

    return redirect(url_for('login_page'))

# Add a new route for managing department fees (admin only) - MySQL version
@app.route('/admin/department-fees', methods=['GET', 'POST'])
def manage_department_fees():
    if session.get('role') != 'admin':
        flash('Unauthorized access', 'error')
        return redirect(url_for('login_page'))

    if request.method == 'POST':
        department = request.form.get('department')
        new_fee = request.form.get('fee')

        if department and new_fee:
            if update_department_fee(department, new_fee):
                flash(f'Fee updated successfully for {department}', 'success')
            else:
                flash('Error updating fee', 'error')

    # Get current fees from MySQL database
    faculty_departments = get_departments_by_faculty()

    return render_template('admin/department_fees.html',
                         faculty_departments=faculty_departments)

# Add this route after the other finance routes
@app.route('/finance/manage-fees', methods=['GET', 'POST'])
def manage_fees():
    """Manage department fees using MySQL database"""
    if 'user_id' not in session or session.get('role') != 'finance':
        return redirect(url_for('login_page'))

    # Get all departments grouped by faculty from MySQL database
    faculty_departments = get_departments_by_faculty()
    print(f"Faculty departments data: {faculty_departments}")
    
    if not faculty_departments:
        flash('No departments found in database', 'error')

    return render_template('finance/manage_fees.html', faculty_departments=faculty_departments)

@app.route('/finance/manage_faculty_departments', methods=['GET', 'POST'])
@login_required
@finance_required
def manage_faculty_departments():
    print("manage_faculty_departments route hit!") # Debug print at start
    """Manage departments and their fees for a specific faculty"""
    try:
        # Get faculty from query parameter and decode it
        faculty_name = request.args.get('faculty')
        print(f"Faculty name from request: {faculty_name}") # Debug print
        if not faculty_name:
            flash('No faculty selected', 'error')
            print("No faculty selected in request.") # Debug print
            return redirect(url_for('manage_fees'))

        # Decode the faculty name
        faculty_name = urllib.parse.unquote(faculty_name)
        print(f"Decoded faculty name: {faculty_name}") # Debug print

        # Validate faculty name
        valid_faculties = [
            'Faculty of Sciences and Information Technology',
            'Faculty of Economics Social Sciences and Management',
            'Faculty of Engineering and Technology',
            'Faculty of Health Sciences',
            'Faculty of Law'
        ]

        if faculty_name not in valid_faculties:
            flash('Invalid faculty selected', 'error')
            print(f"Invalid faculty selected: {faculty_name}") # Debug print
            return redirect(url_for('manage_fees'))

        # Get departments for this faculty from MySQL database
        print("Getting departments from MySQL database...")
        faculty_departments_dict = get_departments_by_faculty()
        faculty_departments = faculty_departments_dict.get(faculty_name, [])

        print(f"Found {len(faculty_departments)} departments for {faculty_name}")

        if not faculty_departments:
            flash('No departments found for this faculty', 'error')
            print(f"No departments found for faculty: {faculty_name}")
            return redirect(url_for('manage_fees'))

        # Handle POST request for updating fees
        if request.method == 'POST':
            print("Received POST request for fee update.") # Debug print
            department = request.form.get('department')
            new_fee = request.form.get('fee')
            print(f"Received data - Department: {department}, New Fee: {new_fee}") # Debug print
            
            if not department or not new_fee:
                print("Missing department or fee information in POST request.") # Debug print
                flash('Missing department or fee information', 'error')
                return redirect(url_for('manage_faculty_departments', faculty=faculty_name))
            
            try:
                new_fee = float(new_fee)
                if new_fee < 0:
                    print(f"Invalid fee amount received: {new_fee}") # Debug print
                    raise ValueError("Fee cannot be negative")
            except ValueError:
                print("ValueError: Invalid fee amount.") # Debug print
                flash('Invalid fee amount', 'error')
                return redirect(url_for('manage_faculty_departments', faculty=faculty_name))
            
            # Update the fee in the MySQL database
            if update_department_fee(department, new_fee):
                print(f"Successfully updated fee for {department} to {new_fee}.")
                flash('Fee updated successfully', 'success')
            else:
                print(f"Failed to update fee for department: {department}")
                flash('Error updating department fee', 'error')
            
            return redirect(url_for('manage_faculty_departments', faculty=faculty_name))

        # For GET request, render the template
        print("Rendering manage_faculty_departments.html template...") # Debug print
        return render_template('finance/manage_faculty_departments.html', 
                               faculty_name=faculty_name, 
                               departments=faculty_departments)

    except Exception as e:
        print(f"Error in manage_faculty_departments: {str(e)}")
        flash('An error occurred while loading the departments', 'error')
        return redirect(url_for('manage_fees'))

# Department initialization removed - now using MySQL database: ines_transcript_system
# All department data is stored in MySQL departments table

# ============================================================================
# COMPREHENSIVE CHATBOT SYSTEM FOR INES TRANSCRIPT SYSTEM
# ============================================================================

def get_chatbot_response(user_message):
    """
    Smart chatbot that provides contextual, appropriately-sized responses
    Understands user intent and responds with relevant, concise information
    """
    try:
        # Normalize the user message
        message = user_message.lower().strip()

        # Analyze user intent for response length
        def analyze_intent(msg):
            detail_indicators = ['complete', 'full', 'detailed', 'step by step', 'guide', 'process', 'how to', 'explain']
            quick_indicators = ['what is', 'what are', 'quick', 'briefly', 'short', 'simple']

            wants_detail = any(indicator in msg for indicator in detail_indicators)
            wants_quick = any(indicator in msg for indicator in quick_indicators)

            return 'detailed' if wants_detail else 'quick' if wants_quick else 'medium'

        intent_level = analyze_intent(message)

        # Smart response function
        def get_smart_response(responses_dict):
            if intent_level in responses_dict:
                return responses_dict[intent_level]
            elif 'responses' in responses_dict:
                import random
                return random.choice(responses_dict['responses'])
            else:
                return responses_dict.get('medium', responses_dict.get('quick', "I can help with that! Please be more specific."))

        # ========== GREETINGS ==========
        if any(word in message for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']):
            greetings = [
                "Hello! 👋 I'm here to help with the INES Transcript System. What can I assist you with?",
                "Hi there! 😊 I can help you with login, requests, payments, or any system questions. What do you need?",
                "Hey! 🌟 I'm your transcript system assistant. How can I help you today?"
            ]
            import random
            return random.choice(greetings)

        if any(phrase in message for phrase in ['how are you', 'how do you do', 'what\'s up', 'whats up']):
            return "I'm doing great and ready to help! 🚀 What would you like to know about the transcript system?"

        # ========== PROBLEM/ISSUE HANDLING ==========
        if any(word in message for word in ['problem', 'issue', 'trouble', 'help me', 'something wrong', 'not working']):
            responses = {
                'quick': "I can help! 😊 What specific issue are you experiencing? Is it with:\n• Login/access\n• Making a request\n• Payment\n• Downloading files\n• Something else?",
                'medium': "I'm here to help with your issue! 🛠️\n\n**Common problems I can help with:**\n• Login difficulties\n• Request submission issues\n• Payment problems\n• File download issues\n• Status tracking questions\n\n**For technical errors:** Visit the Administration Office at INES\n\nWhat specific problem are you facing?",
                'detailed': "I'm ready to help solve your problem! 🛠️\n\n**I can assist with:**\n• **Login Issues** - Forgotten passwords, wrong credentials, department selection\n• **Request Problems** - Form errors, year selection, eligibility questions\n• **Payment Issues** - Failed transactions, payment methods, verification\n• **Download Problems** - Missing files, corrupted downloads, access issues\n• **Status Questions** - Tracking requests, timeline concerns, notifications\n\n**For technical system errors, bugs, or website issues:**\nVisit the Administration Office at INES-Ruhengeri Campus\n📞 Phone: +250 788 123 456\n⏰ Hours: Mon-Fri 8:00 AM - 5:00 PM\n\nPlease describe your specific problem so I can give you the right solution!"
            }
            return get_smart_response(responses)

        # ========== LOGIN HELP ==========
        if any(word in message for word in ['login', 'log in', 'sign in', 'access', 'authentication', 'credentials']):
            responses = {
                'quick': "🔐 **Login Steps:**\n1. Select your role (Student/Finance/Faculty)\n2. Choose department (if student/faculty)\n3. Enter email and password\n4. Click Login\n\n**Demo accounts:** <EMAIL> / password123",
                'medium': "🔐 **How to Login:**\n\n**Steps:**\n1. **Select Role** - Student, Finance, or Faculty\n2. **Choose Department** - Students and Faculty must select\n3. **Enter Credentials** - Use your INES email and password\n4. **Click Login** - Redirects to your dashboard\n\n**Demo Accounts:**\n👨‍🎓 Student: <EMAIL> / password123\n💰 Finance: <EMAIL> / finance123\n👨‍🏫 Faculty: <EMAIL> / faculty123\n\n**Issues?** Check email spelling and password case sensitivity.",
                'detailed': "🔐 **Complete Login Guide:**\n\n**Step-by-Step Process:**\n\n**1. Select Your Role**\n   • **Student** - Request and track transcripts\n   • **Finance** - Payment verification and approval\n   • **Faculty** - Upload and process transcripts\n\n**2. Department/Faculty Selection**\n   • **Students:** Must select your department\n   • **Faculty:** Must select your faculty\n   • **Finance:** No selection needed\n\n**3. Enter Credentials**\n   • **Email:** Official INES email address\n   • **Password:** Case-sensitive password\n\n**4. Access Dashboard**\n   • Each role has different features\n\n**Demo Accounts:**\n👨‍🎓 **Student:** <EMAIL> / password123\n💰 **Finance:** <EMAIL> / finance123\n👨‍🏫 **Faculty:** <EMAIL> / faculty123\n\n**Troubleshooting:**\n• Check email spelling\n• Verify password case\n• Ensure correct department selection\n• Contact admin for account issues\n\n**Technical problems?** Visit Administration Office at INES! 🏫"
            }
            return get_smart_response(responses)

        # ========== TRANSCRIPT REQUEST PROCESS ==========
        if any(word in message for word in ['request', 'transcript', 'apply', 'get transcript', 'how to request']):
            responses = {
                'quick': "📄 **Request Steps:**\n1. Login as student\n2. Click 'Request Transcript'\n3. Select academic years\n4. Make payment (MoMo/Flutterwave)\n5. Track status in dashboard\n\n**Timeline:** 2-5 business days",
                'medium': "📄 **How to Request Transcript:**\n\n**Requirements:**\n• Be registered INES student\n• Completed at least one year\n• Fees up to date\n\n**Process:**\n1. **Login** as student with your department\n2. **Click** 'Request Transcript' from dashboard\n3. **Select** academic years you need\n4. **Review** summary and total cost\n5. **Pay** using MoMo or Flutterwave\n6. **Track** status and download when ready\n\n**Timeline:** 2-5 business days total\n🟡 Payment verification: 1-2 hours\n🟢 Finance approval: 1-2 days\n🔵 Faculty processing: 2-3 days",
                'detailed': "📄 **Complete Transcript Request Guide:**\n\n**Prerequisites:**\n• Registered INES student\n• Completed at least one full academic year\n• All fees up to date\n• Valid email for notifications\n\n**Step-by-Step Process:**\n\n**1. Login to Student Account**\n   • Use student credentials\n   • Select 'Student' role\n   • Choose your department\n\n**2. Start Request**\n   • Click 'Request Transcript'\n   • Personal details auto-populate\n   • Available years will show\n\n**3. Select Academic Years**\n   • Choose years you need\n   • Multiple years allowed\n   • Only completed years available\n\n**4. Confirm Details**\n   • Verify email address\n   • Check all information\n\n**5. Review & Pay**\n   • Check total cost\n   • Choose MoMo or Flutterwave\n   • Complete payment immediately\n\n**6. Track Progress**\n   • Monitor in dashboard\n   • Receive email updates\n   • Download when ready\n\n**Processing Timeline:**\n🟡 Payment Verification: 1-2 hours\n🟢 Finance Approval: 1-2 business days\n🔵 Faculty Processing: 2-3 business days\n✅ Total Time: 2-5 business days\n\nNeed help with a specific step? Just ask! 💫"
            }
            return get_smart_response(responses)

        # ========== PAYMENT INFORMATION ==========
        if any(word in message for word in ['payment', 'pay', 'money', 'cost', 'fee', 'price', 'momo', 'flutterwave', 'how much']):
            responses = {
                'quick': "💳 **Payment Methods:**\n📱 **Mobile Money (MoMo)** - MTN/Airtel, no extra fees\n💳 **Flutterwave** - Visa/Mastercard, small fee\n\n**Common Fees:**\n• Sciences/IT: 750,000 RWF\n• Engineering/Law: 800,000 RWF\n• Education: 650,000 RWF",
                'medium': "💳 **Payment Information:**\n\n**Methods Available:**\n📱 **Mobile Money (MoMo)**\n   • MTN/Airtel Money\n   • Instant processing, no extra fees\n\n💳 **Flutterwave (Cards)**\n   • Visa/Mastercard accepted\n   • Small processing fee\n\n**Fees by Faculty:**\n• **Sciences & IT:** 750,000 RWF\n• **Economics & Management:** 750,000 RWF\n• **Engineering:** 800,000 RWF\n• **Law:** 800,000 RWF\n• **Education:** 650,000 RWF\n• **Health Sciences:** 800,000-900,000 RWF\n\n**Process:** Complete request → Review → Choose payment → Pay → Get confirmation\n\n**Issues?** Contact Finance Office for help! 💰",
                'detailed': "💳 **Complete Payment Information:**\n\n**Available Payment Methods:**\n\n📱 **MTN Mobile Money (MoMo)**\n   • MTN/Airtel Money supported\n   • Instant processing\n   • No additional fees\n   • Most popular option\n\n💳 **Flutterwave (Card Payments)**\n   • Visa and Mastercard\n   • Local and international cards\n   • Secure encryption\n   • Small processing fee may apply\n\n**Payment Process:**\n1. Complete transcript request\n2. Review request summary\n3. Click 'Proceed to Payment'\n4. Choose payment method\n5. Follow instructions\n6. Confirm completion\n7. Receive email confirmation\n\n**Transcript Fees by Faculty:**\n• **Sciences & IT:** 750,000 RWF\n• **Economics & Management:** 750,000 RWF\n• **Engineering:** 800,000 RWF\n• **Law:** 800,000 RWF\n• **Education:** 650,000 RWF\n• **Health Sciences:** 800,000-900,000 RWF\n\n**Troubleshooting:**\n❌ **Payment Failed:** Check balance, try different method\n⏰ **Payment Pending:** Wait 5-10 minutes\n🔒 **Security:** All payments encrypted and secure\n\n**Need help?** Contact Finance Office! 💰"
            }
            return get_smart_response(responses)

        # ========== USER ROLES ==========
        if any(word in message for word in ['role', 'user', 'student', 'finance', 'faculty', 'permission', 'access', 'what can']):
            responses = {
                'quick': "👥 **User Roles:**\n👨‍🎓 **Students:** Request transcripts, make payments, track status, download files\n💰 **Finance:** Verify payments, approve/reject requests\n👨‍🏫 **Faculty:** Upload transcripts, process requests, mark complete\n\nEach role has specific permissions and dashboard features! 🛡️",
                'medium': "👥 **User Roles & What They Can Do:**\n\n👨‍🎓 **STUDENTS:**\n• Request transcripts for completed years\n• Make payments (MoMo/Flutterwave)\n• Track request status\n• Download approved transcripts\n• View payment history\n\n💰 **FINANCE STAFF:**\n• Review and verify payments\n• Approve or reject requests\n• Manage fee structures\n• Generate financial reports\n• Monitor transactions\n\n👨‍🏫 **FACULTY:**\n• Upload transcript files\n• Process approved requests\n• Mark requests as completed\n• Generate academic reports\n• Manage transcript database\n\n**Security:** Each role has specific permissions with no cross-access! 🔐",
                'detailed': "👥 **Complete User Roles & Permissions:**\n\n**👨‍🎓 STUDENT ROLE:**\n• Request transcripts for completed years\n• Make payments using MoMo or Flutterwave\n• Track request status in real-time\n• Download approved transcripts as PDFs\n• View payment history and records\n• Update contact information\n• Receive email notifications\n• Re-request if rejected\n• Select multiple years in one request\n\n**💰 FINANCE STAFF ROLE:**\n• Review payment verification\n• Approve or reject requests\n• Manage department fee structures\n• Generate financial reports\n• Monitor payment transactions\n• Send notifications to students\n• View request history\n• Handle payment disputes\n\n**👨‍🏫 FACULTY ROLE:**\n• Upload transcript files\n• Process finance-approved requests\n• Mark requests as completed\n• Generate academic reports\n• Manage transcript database\n• Send completion notifications\n• Handle re-upload requests\n\n**🔐 Security & Access:**\n• Role-based permissions\n• No cross-role access\n• Secure login required\n• Session management\n• Data protection\n\n**Login Requirements:**\n• Students: Must select department\n• Faculty: Must select faculty\n• Finance: No selection needed\n\nEach role has specific dashboard features! 🛡️"
            }
            return get_smart_response(responses)

        # ========== CONTACT INFORMATION ==========
        if any(word in message for word in ['contact', 'support', 'phone', 'email', 'office', 'administration', 'address']):
            responses = {
                'quick': "📞 **Contact INES:**\n📧 Email: <EMAIL>\n📱 Phone: +250 788 123 456\n🏢 Address: Musanze, Northern Province\n⏰ Hours: Mon-Fri 8AM-5PM, Sat 8AM-12PM\n\n**For technical issues:** Visit Administration Office at INES! 🏫",
                'medium': "📞 **Contact & Support Information:**\n\n**INES-Ruhengeri Main Office:**\n📧 Email: <EMAIL>\n📱 Phone: +250 788 123 456\n🌐 Website: www.ines.ac.rw\n🏢 Address: Musanze, Northern Province, Rwanda\n\n**Office Hours:**\n⏰ Monday-Friday: 8:00 AM - 5:00 PM\n⏰ Saturday: 8:00 AM - 12:00 PM\n⏰ Sunday: Closed\n\n**For Different Issues:**\n• Students: Contact your department office\n• Payment Issues: Finance Office\n• Technical Problems: IT Support\n• Account Problems: Administration Office\n\n**Visit the Administration Office for additional help!** 🏫",
                'detailed': "📞 **Complete Contact & Support Information:**\n\n**INES-RUHENGERI MAIN OFFICE:**\n🏢 Address: Musanze, Northern Province, Rwanda\n📧 Email: <EMAIL>\n📱 Phone: +250 788 123 456\n🌐 Website: www.ines.ac.rw\n📍 Location: INES-Ruhengeri Campus\n\n**OFFICE HOURS:**\n📅 Monday-Friday: 8:00 AM - 5:00 PM\n📅 Saturday: 8:00 AM - 12:00 PM\n📅 Sunday: Closed\n\n**DEPARTMENT-SPECIFIC SUPPORT:**\n👨‍🎓 **For Students:** Contact your department office\n💰 **For Payment Issues:** Finance Office\n🔧 **For Technical Problems:** IT Support\n👨‍🏫 **For Faculty Matters:** Faculty Office\n\n**EMERGENCY CONTACT:**\n📞 24/7 Emergency: +250 788 123 456\n📧 Emergency Email: <EMAIL>\n\n**PHYSICAL LOCATION:**\n• Campus: Main INES-Ruhengeri Campus\n• Building: Administration Block\n• Floor: Ground Floor\n• Parking: Available on campus\n\n**WHAT TO BRING:**\n• Valid ID (National ID or Student ID)\n• Request reference number\n• Payment receipts (if applicable)\n\n**For additional help, visit the Administration Office!** 🏫"
            }
            return get_smart_response(responses)

        # ========== STATUS TRACKING ==========
        if any(word in message for word in ['status', 'track', 'check', 'where', 'when', 'timeline', 'progress', 'how long']):
            responses = {
                'quick': "📊 **Track Status:**\n1. Login to student dashboard\n2. Click 'Request Status'\n3. Check current progress\n\n**Status Types:**\n🟡 Pending Finance (1-2 hours)\n🟢 Finance Approved (1-2 days)\n🔵 Faculty Processing (2-3 days)\n✅ Completed (Download ready)",
                'medium': "📊 **Request Status & Tracking:**\n\n**How to Track:**\n1. **Login** to your student dashboard\n2. **Click** 'Request Status' or 'View Downloads'\n3. **Check** current status and progress\n4. **Monitor** email notifications\n\n**Status Types:**\n🟡 **Pending Finance** - Payment verification (1-2 hours)\n🟢 **Approved Finance** - Sent to faculty (1-2 days)\n🔵 **Faculty Processing** - Transcript preparation (2-3 days)\n✅ **Completed** - Ready for download\n🔴 **Rejected** - Issue with payment/eligibility\n\n**Total Timeline:** 2-5 business days\n\n**Email notifications sent at each stage!** 📧",
                'detailed': "📊 **Complete Request Status & Tracking Guide:**\n\n**Request Status Types Explained:**\n\n🟡 **Pending Finance** - Payment verification in progress\n   • Your payment is being verified by finance staff\n   • Usually takes 1-2 hours for automatic verification\n   • Manual review may take up to 24 hours\n\n🟢 **Approved Finance** - Payment confirmed, sent to faculty\n   • Finance has verified your payment successfully\n   • Request forwarded to faculty for processing\n   • Faculty will now prepare your transcript\n\n🔵 **Faculty Processing** - Transcript being prepared\n   • Faculty is generating your official transcript\n   • Academic records being verified and compiled\n   • Quality review and formatting in progress\n\n✅ **Completed** - Transcript ready for download\n   • Your transcript has been uploaded to the system\n   • Download link is now active in your dashboard\n   • Email notification sent with download instructions\n\n🔴 **Rejected** - Payment or eligibility issue\n   • Issue with payment verification or eligibility\n   • Check rejection reason in your dashboard\n   • Contact finance office or resubmit request\n\n**How to Track Your Request:**\n1. **Login** to your student dashboard\n2. **Click** 'Request Status' or 'View Downloads'\n3. **Check** current status and progress\n4. **Monitor** email notifications for updates\n5. **Refresh** dashboard for real-time status\n\n**Processing Timeline:**\n🟡 Payment Verification: 1-2 hours\n🟢 Finance Review: 1-2 business days\n🔵 Faculty Processing: 2-3 business days\n✅ Total Time: 2-5 business days\n\n**Email Notifications You'll Receive:**\n• Payment confirmation\n• Finance approval notification\n• Faculty processing update\n• Completion and download notification\n\n**Track your progress in real-time through your dashboard!** ⏰"
            }
            return get_smart_response(responses)

        # ========== DOWNLOAD HELP ==========
        if any(word in message for word in ['download', 'get transcript', 'completed', 'ready', 'pdf', 'file']):
            responses = {
                'quick': "📥 **Download Steps:**\n1. Login to student dashboard\n2. Go to 'View Downloads'\n3. Find completed request\n4. Click download button\n5. Save PDF file\n\n**Requirements:** Request status must show 'Completed' ✅",
                'medium': "📥 **How to Download Transcript:**\n\n**Prerequisites:**\n• Request status shows 'Completed'\n• You're logged into student account\n• Payment verified and approved\n\n**Download Process:**\n1. **Login** to your student dashboard\n2. **Navigate** to 'View Downloads'\n3. **Locate** your completed request\n4. **Click** the download button\n5. **Save** PDF to your device\n6. **Verify** content and watermark\n\n**File Details:**\n• Format: PDF with INES watermark\n• Security: Password-protected\n• Size: 1-3 MB typically\n• Multiple downloads allowed\n\n**Issues?** Try different browser or contact Faculty Office! 📞",
                'detailed': "📥 **Complete Transcript Download Guide:**\n\n**Prerequisites for Download:**\n• Request status must show 'Completed'\n• Payment must be verified and approved\n• Faculty processing must be finished\n• You must be logged into your student account\n\n**Step-by-Step Download Process:**\n\n**1. Login to Your Account**\n   • Use your student credentials\n   • Access your student dashboard\n   • Ensure you're in the correct account\n\n**2. Navigate to Downloads**\n   • Click 'View Downloads' from main menu\n   • Or go to 'Request Status' page\n   • Look for completed requests\n\n**3. Locate Your Request**\n   • Find your completed transcript request\n   • Status should show 'Completed' with green checkmark\n   • Download button should be visible and active\n\n**4. Download the File**\n   • Click the download button next to your request\n   • File will download as PDF format\n   • Save to your preferred location on device\n\n**5. Verify the Download**\n   • Open the PDF file to verify content\n   • Check that all requested years are included\n   • Ensure official INES watermark is present\n\n**Download Features & Security:**\n• **Secure PDF Format** - Official transcript format\n• **INES Watermark** - Authentic institutional marking\n• **Digital Signatures** - Verified authenticity\n• **Multiple Downloads** - Can download multiple times\n• **No Expiration** - Files remain available indefinitely\n\n**Troubleshooting Download Issues:**\n❌ **File Not Appearing?** Check status is 'Completed', refresh page\n❌ **Download Failed?** Try different browser (Chrome recommended)\n❌ **PDF Won't Open?** Download again, try different PDF reader\n\n**You can download your transcript multiple times once it's available!** ✅"
            }
            return get_smart_response(responses)

        # ========== TECHNICAL ISSUES ==========
        if any(word in message for word in ['error', 'bug', 'broken', 'technical', 'system error', 'website']):
            return "🔧 **Technical Issues & Troubleshooting:**\n\n**I can only help with HOW TO USE the system, not fix technical problems.**\n\n**For all technical issues, errors, or bugs:**\n\n**🏢 VISIT THE ADMINISTRATION OFFICE AT INES**\n📍 **Location:** INES-Ruhengeri Campus, Administration Block\n⏰ **Hours:** Monday-Friday 8:00 AM - 5:00 PM\n📞 **Phone:** +250 788 123 456\n\n**🔧 CONTACT IT SUPPORT TEAM**\n📧 **Email:** <EMAIL>\n\n**COMMON QUICK FIXES TO TRY:**\n✅ **Refresh the page** (F5 or Ctrl+R)\n✅ **Clear browser cache and cookies**\n✅ **Try a different browser** (Chrome recommended)\n✅ **Check internet connection**\n\n**I'm here to help you understand how to use the system once it's working properly!** 😊"

        # ========== ELIGIBILITY ==========
        if any(word in message for word in ['eligible', 'qualify', 'can i get', 'requirements', 'criteria']):
            responses = {
                'quick': "✅ **Eligibility Requirements:**\n• Be registered INES student\n• Completed at least one full academic year\n• All fees up to date\n• Valid email for notifications\n• Good academic standing\n\n**Check eligibility:** Login and view available years in request form! 🎓",
                'medium': "✅ **Transcript Eligibility & Requirements:**\n\n**WHO CAN REQUEST:**\n• **Current INES Students** - Enrolled with completed years\n• **Former INES Graduates** - Alumni with valid accounts\n• **Students with Completed Years** - Finalized academic records\n\n**REQUIREMENTS:**\n📚 **Academic:** At least one completed academic year\n💰 **Financial:** All tuition fees up to date\n🔐 **Account:** Valid INES account and credentials\n📧 **Communication:** Valid email for notifications\n\n**WHAT YOU CAN REQUEST:**\n✅ **Completed Academic Years** - Only finalized years\n✅ **Multiple Years** - Several years together\n✅ **Official Transcripts** - Watermarked documents\n\n**CANNOT REQUEST:**\n❌ **Incomplete Years** - Current/unfinished semesters\n❌ **Provisional Records** - Temporary documents\n\n**Check your eligibility by logging in and viewing available years!** 🎓",
                'detailed': "✅ **Complete Transcript Eligibility & Requirements:**\n\n**WHO CAN REQUEST TRANSCRIPTS:**\n\n**✅ CURRENT INES STUDENTS**\n• Must be currently enrolled at INES-Ruhengeri\n• Must have completed at least one full academic year\n• Must have valid student account and credentials\n• Must be in good academic standing\n\n**✅ FORMER INES GRADUATES**\n• Alumni who completed their studies at INES\n• Must have valid alumni account access\n• Can request transcripts for completed programs\n• No time limit on transcript requests\n\n**DETAILED REQUIREMENTS:**\n\n**📚 ACADEMIC REQUIREMENTS:**\n• **Minimum:** One completed academic year\n• **Status:** Academic records must be finalized\n• **Grades:** All courses must have final grades\n• **Standing:** Must be in good academic standing\n\n**💰 FINANCIAL REQUIREMENTS:**\n• **Fee Status:** All tuition fees must be up to date\n• **Outstanding Debts:** No unpaid institutional debts\n• **Payment Ability:** Must be able to pay transcript fees\n\n**🔐 ACCOUNT REQUIREMENTS:**\n• **Valid Account:** Must have active INES account\n• **Correct Credentials:** Working email and password\n• **Department Selection:** Must select correct department\n• **Contact Info:** Valid email for notifications\n\n**WHAT YOU CAN REQUEST:**\n✅ **Completed Academic Years** - Only finalized years\n✅ **Multiple Years** - Can request several years together\n✅ **Official Transcripts** - Sealed and watermarked documents\n✅ **Digital Copies** - PDF format downloads\n\n**CANNOT REQUEST:**\n❌ **Incomplete Years** - Current or unfinished semesters\n❌ **Provisional Records** - Temporary or unofficial records\n❌ **Future Years** - Years not yet completed\n\n**HOW TO CHECK ELIGIBILITY:**\n1. **Login** to your student account\n2. **Go to** Request Transcript page\n3. **View** available academic years\n4. **Check** if your completed years appear\n\n**Check your eligibility by logging in and viewing available years in the request form!** 🎓"
            }
            return get_smart_response(responses)

        # If no specific pattern matches, return general help
        return """😊 **I'm your INES Transcript System Assistant!**

I can help you with detailed information about:

🔐 **Login Process** - Step-by-step login guide with demo accounts
📄 **Transcript Requests** - Complete request process and requirements
💳 **Payment Methods** - MoMo, Flutterwave, fees by department
👥 **User Roles** - Student, Finance, Faculty permissions and features
📊 **Status Tracking** - How to monitor your request progress
🏫 **Departments & Fees** - All faculties and their transcript costs
📞 **Contact Information** - Support offices and administration
⏰ **Processing Times** - Detailed timeline and expectations
📥 **Downloads** - How to get your completed transcripts
🔧 **Troubleshooting** - Common issues and solutions

**Try asking me something like:**
• "How do I login to the system?"
• "What is the transcript request process?"
• "What payment methods are available?"
• "What can students do in the system?"
• "How much do transcripts cost?"
• "How long does processing take?"
• "I have a problem with..."

**For technical issues or system errors, please visit the Administration Office at INES!**

What specific information would you like to know? 🤔"""

    except Exception as e:
        print(f"Chatbot error: {e}")
        return "I'm here to help with the INES Transcript System! Please try asking about login, requests, payments, or user roles. 😊"

# Chatbot test page route
@app.route('/chatbot-test')
def chatbot_test():
    return render_template('chatbot_test.html')

# Chatbot route
@app.route('/chat', methods=['POST'])
def chat():
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        user_message = data.get('message', '').strip()

        if not user_message:
            return jsonify({'response': 'Please type a message! I\'m here to help with the transcript system. 😊'})

        # Get response from chatbot
        response = get_chatbot_response(user_message)

        return jsonify({'response': response})

    except Exception as e:
        print(f"Chat error: {e}")
        # Provide helpful fallback response
        fallback_response = """I'm having trouble right now, but I'm here to help! 😔

**I can assist you with:**
🔐 Login and authentication
📄 Transcript request process
💳 Payment methods and fees
👥 User roles and permissions
📊 Status tracking and timelines
📞 Contact information

**Try asking:**
• 'How do I login?'
• 'How to request transcript?'
• 'What are payment methods?'

**For technical issues, visit the Administration Office at INES!** 🏫"""

        return jsonify({'response': fallback_response})

# Redis Health Check Endpoints
@app.route('/health/redis')
def redis_health():
    """Redis health check endpoint"""
    if not REDIS_AVAILABLE:
        return jsonify({
            'service': 'redis',
            'status': 'not_available',
            'message': 'Redis modules not installed',
            'timestamp': datetime.now().isoformat()
        }), 200

    try:
        redis_service = get_redis_service()
        if not redis_service:
            return jsonify({
                'service': 'redis',
                'status': 'not_configured',
                'message': 'Redis service not initialized',
                'timestamp': datetime.now().isoformat()
            }), 200

        health_status = redis_service.health_check()

        return jsonify({
            'service': 'redis',
            'timestamp': datetime.now().isoformat(),
            **health_status
        }), 200 if health_status['status'] in ['healthy', 'fallback'] else 500

    except Exception as e:
        return jsonify({
            'service': 'redis',
            'status': 'error',
            'message': f'Health check failed: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/health/cache-stats')
@login_required
def cache_stats():
    """Get cache statistics (admin only)"""
    if session.get('role') not in ['admin', 'finance']:
        return jsonify({'error': 'Unauthorized'}), 403

    if not REDIS_AVAILABLE:
        return jsonify({
            'cache_system': 'none',
            'message': 'Redis not available - no caching active',
            'session_interface': 'default',
            'timestamp': datetime.now().isoformat()
        })

# Debug route for checking upload status
@app.route('/debug/upload-status/<request_id>')
def debug_upload_status(request_id):
    """Debug route to check upload status"""
    try:
        from new_database_service import get_db_connection

        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get request details
                cursor.execute("""
                    SELECT tr.*, tr.student_name, tr.student_reg_no as student_id, tr.student_email as email,
                           tr.department, tr.faculty
                    FROM transcript_requests tr
                    WHERE tr.id = %s
                """, (request_id,))

                request_data = cursor.fetchone()

                # Get transcript file info
                cursor.execute("""
                    SELECT * FROM transcript_files WHERE request_id = %s
                """, (request_id,))

                transcript_file = cursor.fetchone()

                return jsonify({
                    'request_id': request_id,
                    'request_found': request_data is not None,
                    'request_data': dict(request_data) if request_data else None,
                    'transcript_file_found': transcript_file is not None,
                    'transcript_file': dict(transcript_file) if transcript_file else None,
                    'status': request_data['status'] if request_data else 'Not Found',
                    'timestamp': datetime.now().isoformat()
                })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'request_id': request_id,
            'timestamp': datetime.now().isoformat()
        }), 500

    try:
        redis_service = get_redis_service()
        if not redis_service:
            return jsonify({
                'cache_system': 'none',
                'message': 'Redis service not initialized',
                'session_interface': 'default',
                'timestamp': datetime.now().isoformat()
            })

        health_status = redis_service.health_check()

        return jsonify({
            'cache_system': 'redis',
            'timestamp': datetime.now().isoformat(),
            'health': health_status,
            'session_interface': 'redis' if hasattr(app.session_interface, 'redis_service') else 'default'
        })

    except Exception as e:
        return jsonify({
            'error': f'Failed to get cache stats: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    # Database is ready - using simplified schema
    print("Database schema ready - using simplified tables")
    print("Finance users ready - using users table")
    print("Starting INES Transcript System...")
    app.run(debug=True)
