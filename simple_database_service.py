"""
Database Service - Fixed for actual table structure
Matches the real database columns and relationships
"""
import pymysql
import json
import bcrypt
from datetime import datetime
from contextlib import contextmanager

DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4'
}

@contextmanager
def get_db_connection():
    connection = pymysql.connect(**DB_CONFIG)
    try:
        yield connection
    finally:
        connection.close()

def authenticate_user(email, password, department=None, role=None):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT * FROM users WHERE email = %s AND role = %s AND is_active = 1", (email, role))
            user = cursor.fetchone()
            
            if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                # Check department for students (use department_name column)
                if role == 'student' and department and user.get('department_name') != department:
                    return None
                # Check faculty for faculty (use faculty_name column)
                if role == 'faculty' and department and user.get('faculty_name') != department:
                    return None
                    
                cursor.execute("UPDATE users SET last_login = NOW() WHERE id = %s", (user['id'],))
                conn.commit()
                del user['password_hash']
                return user
        return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def add_request(student_reg_no, academic_years, payment_method, total_price, **kwargs):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            # Get student by reg_no
            cursor.execute("SELECT * FROM users WHERE reg_no = %s AND role = 'student'", (student_reg_no,))
            student = cursor.fetchone()
            if not student:
                print(f"Student not found: {student_reg_no}")
                return None

            # Generate request number
            year = datetime.now().year
            cursor.execute("SELECT COUNT(*) FROM transcript_requests WHERE YEAR(created_at) = %s", (year,))
            count_result = cursor.fetchone()
            count = count_result['COUNT(*)'] + 1
            request_number = f"REQ-{year}-{count:04d}"

            # Convert academic_years to JSON string if it's a list
            if isinstance(academic_years, list):
                academic_years_json = json.dumps(academic_years)
            else:
                academic_years_json = academic_years

            # Insert request with updated schema including payment proof filename
            cursor.execute("""
                INSERT INTO transcript_requests
                (student_id, request_number, academic_years, total_transcripts, total_amount,
                 payment_method, academic_year, amount_paid, payment_status, payment_proof_filename, notes)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (student['id'], request_number, academic_years_json, len(academic_years) if isinstance(academic_years, list) else 1,
                  total_price, payment_method or 'mobile_money', kwargs.get('academic_year'),
                  kwargs.get('amount_paid', 0), 'pending', kwargs.get('payment_proof_filename'), kwargs.get('notes', 'Transcript request')))

            request_id = cursor.lastrowid
            if request_id:
                conn.commit()
                print(f"Request created successfully: ID {request_id}")

                return {
                    'id': request_id,
                    'request_number': request_number,
                    'student_id': student_reg_no,
                    'student_name': student['name'],
                    'student_email': student['email'],
                    'department': student['department_name'],
                    'academic_years': academic_years,
                    'total_price': total_price,
                    'payment_method': payment_method,
                    'status': 'pending'
                }
            else:
                print("No request ID returned from insert")
                return None

    except Exception as e:
        print(f"Error adding request: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return None

def get_requests_by_student_id(student_reg_no):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            # Join with users table and files table to get requests by reg_no
            cursor.execute("""
                SELECT tr.*, u.name as student_name, u.reg_no, u.department_name, u.faculty_name,
                       f.filename as transcript_file, f.file_path
                FROM transcript_requests tr
                JOIN users u ON tr.student_id = u.id
                LEFT JOIN files f ON tr.id = f.request_id AND f.file_type = 'transcript'
                WHERE u.reg_no = %s
                ORDER BY tr.created_at DESC
            """, (student_reg_no,))
            requests = cursor.fetchall()
            
            result = []
            for req in requests:
                # Determine proper status based on payment_status, auto_decision, and completion
                payment_status = req.get('payment_status', 'pending')
                auto_decision = req.get('auto_decision')
                completed_at = req.get('completed_at')
                last_downloaded = req.get('last_downloaded')

                # Map to proper status for student display
                if payment_status == 'failed' or auto_decision == 'rejected':
                    status = 'rejected'
                elif completed_at and last_downloaded:
                    status = 'done'  # Downloaded
                elif completed_at:
                    status = 'completed'  # Ready for download
                elif payment_status == 'paid':
                    status = 'approved_finance'  # Approved by finance, sent to faculty
                else:
                    status = 'pending_finance'  # Waiting for finance

                # Extract rejection reason from notes or auto_decision_reason
                rejection_reason = ''
                if status == 'rejected':
                    if req.get('auto_decision_reason'):
                        rejection_reason = req.get('auto_decision_reason')
                    elif 'REJECTED:' in req.get('notes', ''):
                        # Extract rejection reason from notes
                        notes = req.get('notes', '')
                        if 'REJECTED:' in notes:
                            parts = notes.split('REJECTED:')
                            if len(parts) > 1:
                                rejection_reason = parts[1].split('(by finance')[0].strip()
                    else:
                        rejection_reason = 'Request rejected by finance'

                result.append({
                    'id': str(req['id']),
                    'student_id': req['reg_no'],
                    'student_name': req['student_name'],
                    'department': req.get('department_name', 'Unknown'),
                    'academic_years': json.loads(req['academic_years']) if req['academic_years'] else [],
                    'total_price': float(req['total_amount']),
                    'payment_method': req['payment_method'],
                    'status': status,  # Properly mapped status
                    'payment_status': payment_status,
                    'date': req['created_at'].strftime('%Y-%m-%d'),
                    'rejection_reason': rejection_reason,
                    'download_count': 1 if req.get('last_downloaded') else 0,
                    'finance_confirmed_at': req.get('finance_confirmed_at'),
                    'auto_decision': req.get('auto_decision'),
                    'auto_decision_reason': req.get('auto_decision_reason'),
                    'approved_date': req.get('approved_date'),
                    'completed_at': req.get('completed_at'),
                    'last_downloaded': req.get('last_downloaded'),
                    'faculty_override': req.get('faculty_override'),
                    'faculty_override_reason': req.get('faculty_override_reason'),
                    'payment_confirmed_at': req.get('payment_confirmed_at'),
                    'payment_confirmed_by': req.get('payment_confirmed_by')
                })
            return result
    except Exception as e:
        print(f"Error getting requests: {e}")
        return []

def get_all_requests():
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("""
                SELECT tr.*, u.name as student_name, u.reg_no, u.email, u.department_name
                FROM transcript_requests tr
                JOIN users u ON tr.student_id = u.id
                ORDER BY tr.created_at DESC
            """)
            requests = cursor.fetchall()
            
            result = []
            for req in requests:
                result.append({
                    'id': str(req['id']),
                    'student_id': req['reg_no'],
                    'student_name': req['student_name'],
                    'department': req.get('department_name', 'Unknown'),
                    'academic_years': json.loads(req['academic_years']),
                    'total_price': float(req['total_amount']),
                    'payment_method': req['payment_method'],
                    'status': req.get('payment_status', 'pending'),
                    'payment_status': req.get('payment_status', 'pending'),  # Include both fields
                    'date': req['created_at'].strftime('%Y-%m-%d'),
                    'rejection_reason': req.get('auto_decision_reason'),  # Use auto_decision_reason
                    'finance_confirmed_at': req.get('finance_confirmed_at'),
                    'completed_at': req.get('completed_at'),  # Add completed_at field
                    'email': req['email']
                })
            return result
    except Exception as e:
        print(f"Error getting all requests: {e}")
        return []

def update_request_status(request_id, status, rejection_reason=None):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            if status == 'rejected':
                cursor.execute("""
                    UPDATE transcript_requests 
                    SET status = %s, rejection_reason = %s, rejected_at = NOW() 
                    WHERE id = %s
                """, (status, rejection_reason, request_id))
            else:
                cursor.execute("UPDATE transcript_requests SET status = %s WHERE id = %s", (status, request_id))
            conn.commit()
            return cursor.rowcount > 0
    except Exception as e:
        print(f"Error updating status: {e}")
        return False

def add_transcript(request_id, student_reg_no, student_name, academic_years, filename):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("SELECT * FROM transcript_requests WHERE id = %s", (request_id,))
            request_data = cursor.fetchone()
            if not request_data:
                return False
            
            # Get student ID from the request
            student_id = request_data['student_id']

            # Add file record with student_id
            cursor.execute("""
                INSERT INTO files (request_id, student_id, file_type, filename, file_path, uploaded_by)
                VALUES (%s, %s, 'transcript', %s, %s, %s)
            """, (request_id, student_id, filename, f"static/uploads/{filename}", student_reg_no))
            
            # Update request status to completed (keep payment_status as 'paid', use completed_at to track completion)
            cursor.execute("""
                UPDATE transcript_requests
                SET completed_at = NOW()
                WHERE id = %s
            """, (request_id,))
            
            if cursor.rowcount > 0:
                # Get complete request data for notification
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.email as student_email, u.reg_no as student_id
                    FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s
                """, (request_id,))
                complete_request = cursor.fetchone()
                
                if complete_request:
                    # Convert to dict format for notifications
                    notification_data = {
                        'id': complete_request['id'],
                        'student_name': complete_request['student_name'],
                        'email': complete_request['student_email'],
                        'student_id': complete_request['student_id'],
                        'academic_years': json.loads(complete_request['academic_years'])
                    }
                    
                    # Send completion notification
                    try:
                        from notification_service import notify_student_transcript_ready
                        notify_student_transcript_ready(notification_data)
                    except Exception as e:
                        print(f"Notification error: {e}")
                
                conn.commit()
                return True
            
            return False
    except Exception as e:
        print(f"Add transcript error: {e}")
        return False

def get_finance_dashboard_data():
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("""
                SELECT tr.*, u.name as student_name, u.reg_no, u.email, u.department_name,
                       d.transcript_fee as total_fees_required
                FROM transcript_requests tr
                JOIN users u ON tr.student_id = u.id
                LEFT JOIN departments d ON u.department_name = d.name
                ORDER BY tr.created_at DESC
            """)
            
            all_requests = cursor.fetchall()
            
            pending_requests = []
            approved_requests = []
            rejected_requests = []
            
            for req in all_requests:
                # Calculate school fees paid for requested academic years only
                academic_years = json.loads(req['academic_years']) if req['academic_years'] else []
                school_fees_paid = 0
                
                for year in academic_years:
                    cursor.execute("""
                        SELECT COALESCE(SUM(amount_paid), 0) as paid
                        FROM student_payments 
                        WHERE student_id = %s AND academic_year = %s
                    """, (req['student_id'], year))
                    year_payment = cursor.fetchone()
                    if year_payment:
                        school_fees_paid += float(year_payment['paid'])
                
                # Extract rejection reason and date
                rejection_reason = 'No reason provided'
                rejected_date = 'N/A'

                if req.get('auto_decision') == 'rejected':
                    if req.get('auto_decision_reason'):
                        rejection_reason = req.get('auto_decision_reason')
                    elif 'REJECTED:' in req.get('notes', ''):
                        # Extract rejection reason from notes
                        notes = req.get('notes', '')
                        if 'REJECTED:' in notes:
                            parts = notes.split('REJECTED:')
                            if len(parts) > 1:
                                rejection_reason = parts[1].split('(by finance')[0].strip()

                    # Use auto_processed_at as rejection date
                    if req.get('auto_processed_at'):
                        rejected_date = req['auto_processed_at'].strftime('%Y-%m-%d %H:%M')

                formatted_req = {
                    'id': str(req['id']),
                    'student_id': req['reg_no'],
                    'student_name': req['student_name'],
                    'student_email': req['email'],
                    'department': req.get('department_name', 'Unknown'),
                    'academic_years': academic_years,
                    'total_price': float(req['total_amount']),
                    'payment_method': req['payment_method'] or 'Not specified',
                    'status': req.get('payment_status', 'pending'),
                    'payment_status': req.get('payment_status', 'pending'),
                    'date': req['created_at'].strftime('%Y-%m-%d'),
                    'rejection_reason': rejection_reason,
                    'approved_date': req['finance_confirmed_at'].strftime('%Y-%m-%d %H:%M') if req.get('finance_confirmed_at') else req['created_at'].strftime('%Y-%m-%d %H:%M'),
                    'rejected_date': rejected_date,
                    'payment_proof_filename': req.get('payment_proof_filename'),
                    'total_fees_required': float(req['total_fees_required']) * len(academic_years) if req['total_fees_required'] else 0,
                    'school_fees_paid': school_fees_paid,
                    'email': req['email']  # Add email for notifications
                }
                
                # Use payment_status and auto_decision for categorization
                payment_status = req.get('payment_status', 'pending')
                auto_decision = req.get('auto_decision')

                if payment_status == 'pending':
                    pending_requests.append(formatted_req)
                elif payment_status == 'paid':
                    approved_requests.append(formatted_req)
                elif payment_status == 'failed' or auto_decision == 'rejected':
                    rejected_requests.append(formatted_req)
            
            return {
                'pending_requests': pending_requests,
                'approved_requests': approved_requests,
                'rejected_requests': rejected_requests,
                'pending_count': len(pending_requests),
                'approved_count': len(approved_requests),
                'rejected_count': len(rejected_requests),
                'total_count': len(all_requests)
            }
            
    except Exception as e:
        print(f"Finance dashboard error: {str(e)}")
        return {'pending_requests': [], 'approved_requests': [], 'rejected_requests': [], 
                'pending_count': 0, 'approved_count': 0, 'rejected_count': 0, 'total_count': 0}

def approve_transcript_request(request_id, finance_user_reg_no):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Get finance user ID
            cursor.execute("SELECT id FROM users WHERE reg_no = %s", (finance_user_reg_no,))
            finance_user = cursor.fetchone()
            finance_user_id = finance_user['id'] if finance_user else None
            
            # Update request status and send notifications
            cursor.execute("""
                UPDATE transcript_requests
                SET payment_status = 'paid', finance_confirmed_by = %s, finance_confirmed_at = NOW()
                WHERE id = %s
            """, (finance_user_id, request_id))
            
            if cursor.rowcount > 0:
                # Get request data for notifications
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.email as student_email, u.reg_no as student_id
                    FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s
                """, (request_id,))
                request_data = cursor.fetchone()
                
                if request_data:
                    # Get department name from users table
                    cursor.execute("SELECT department_name FROM users WHERE id = %s", (request_data['student_id'],))
                    dept_result = cursor.fetchone()
                    department = dept_result['department_name'] if dept_result else 'Unknown'
                    
                    # Convert to dict format for notifications
                    notification_data = {
                        'id': request_data['id'],
                        'student_name': request_data['student_name'],
                        'student_email': request_data['student_email'],
                        'email': request_data['student_email'],  # Add email field
                        'student_id': request_data['student_id'],
                        'academic_years': json.loads(request_data['academic_years']),
                        'department': department
                    }
                    
                    # Send notifications with timeout handling
                    try:
                        import threading
                        from notification_service import notify_student_approval, notify_faculty_new_request

                        # Send notifications in background to avoid delays
                        def send_notifications():
                            try:
                                notify_student_approval(notification_data)
                                notify_faculty_new_request(notification_data)
                            except Exception as e:
                                print(f"Background notification error: {e}")

                        # Start notification thread
                        notification_thread = threading.Thread(target=send_notifications)
                        notification_thread.daemon = True
                        notification_thread.start()

                    except Exception as e:
                        print(f"Notification setup error: {e}")
                
                conn.commit()
                return True
            
            return False
    except Exception as e:
        print(f"Approve request error: {e}")
        return False

def reject_transcript_request(request_id, finance_user_reg_no, reason=None):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Get finance user ID
            cursor.execute("SELECT id FROM users WHERE reg_no = %s", (finance_user_reg_no,))
            finance_user = cursor.fetchone()
            finance_user_id = finance_user['id'] if finance_user else None
            
            final_reason = reason or 'Payment verification failed'
            
            # Update payment_status to failed and add rejection info to notes
            cursor.execute("""
                UPDATE transcript_requests
                SET payment_status = 'failed',
                    notes = CONCAT(notes, '\nREJECTED: ', %s, ' (by finance user ID: ', %s, ' at ', NOW(), ')'),
                    auto_decision = 'rejected',
                    auto_decision_reason = %s,
                    auto_processed_at = NOW()
                WHERE id = %s
            """, (final_reason, finance_user_id, final_reason, request_id))
            
            if cursor.rowcount > 0:
                # Get request data for notifications
                cursor.execute("""
                    SELECT tr.*, u.name as student_name, u.email as student_email, u.reg_no as student_id
                    FROM transcript_requests tr
                    JOIN users u ON tr.student_id = u.id
                    WHERE tr.id = %s
                """, (request_id,))
                request_data = cursor.fetchone()
                
                if request_data:
                    # Convert to dict format for notifications
                    notification_data = {
                        'id': request_data['id'],
                        'student_name': request_data['student_name'],
                        'email': request_data['student_email'],
                        'student_email': request_data['student_email'],  # Add both formats
                        'student_id': request_data['student_id'],
                        'academic_years': json.loads(request_data['academic_years']),
                        'rejection_reason': final_reason
                    }
                    
                    # Send rejection notification
                    try:
                        from notification_service import notify_student_rejection
                        notify_student_rejection(notification_data, final_reason)
                    except Exception as e:
                        print(f"Rejection notification error: {e}")
                
                conn.commit()
                return True
            
            return False
    except Exception as e:
        print(f"Reject request error: {e}")
        return False

def get_departments_by_faculty():
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            # Updated query to match new database schema
            cursor.execute("SELECT name, transcript_fee, faculty FROM departments ORDER BY faculty, name")
            departments = cursor.fetchall()
            print(f"Found {len(departments)} departments in database")

            result = {}
            for dept in departments:
                faculty = dept['faculty']  # Faculty is now stored as string directly
                if faculty not in result:
                    result[faculty] = []
                result[faculty].append({
                    'name': dept['name'],
                    'fee': float(dept['transcript_fee'])  # Use transcript_fee column
                })

            print(f"Final result: {result}")
            return result
    except Exception as e:
        print(f"Error getting departments: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return {}

def update_department_fee(department_name, new_fee):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # Updated to use transcript_fee column
            cursor.execute("UPDATE departments SET transcript_fee = %s WHERE name = %s", (float(new_fee), department_name))
            conn.commit()
            return cursor.rowcount > 0
    except Exception as e:
        print(f"Error updating department fee: {e}")
        return False

def get_department_fee(department_name):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # Updated to use transcript_fee column
            cursor.execute("SELECT transcript_fee FROM departments WHERE name = %s", (department_name,))
            result = cursor.fetchone()
            return float(result[0]) if result else 750000.0
    except:
        return 750000.0

def delete_transcript_request(request_id, finance_user_reg_no):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM files WHERE request_id = %s", (request_id,))
            cursor.execute("DELETE FROM transcript_requests WHERE id = %s", (request_id,))
            conn.commit()
            return cursor.rowcount > 0
    except Exception as e:
        print(f"Delete request error: {e}")
        return False

def get_department_emails(role):
    emails = []
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT email FROM users WHERE role = %s AND is_active = 1", (role,))
            results = cursor.fetchall()
            emails = [row[0] for row in results]
    except Exception as e:
        print(f"Error getting {role} emails: {e}")
    return emails

def test_connection():
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            count = cursor.fetchone()[0]
            print(f"Connected. {count} users found.")
            return True
    except Exception as e:
        print(f"Connection failed: {e}")
        return False

def get_student_payment_summary(student_id, academic_years):
    """Get payment summary for finance validation"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Get student info
            cursor.execute("SELECT reg_no, department_name FROM users WHERE id = %s", (student_id,))
            student = cursor.fetchone()
            if not student:
                return None
            
            # Get department fee
            cursor.execute("SELECT total_fee FROM departments WHERE name = %s", (student['department_name'],))
            dept_fee = cursor.fetchone()
            annual_fee = float(dept_fee['total_fee']) if dept_fee else 750000.0
            
            # Get payments for each academic year
            payment_summary = []
            total_required = 0
            total_paid = 0
            
            for year in academic_years:
                cursor.execute("""
                    SELECT COALESCE(SUM(amount_paid), 0) as paid_amount
                    FROM student_payments 
                    WHERE student_id = %s AND academic_year = %s
                """, (student_id, year))
                
                payment = cursor.fetchone()
                paid_amount = float(payment['paid_amount']) if payment else 0.0
                
                payment_summary.append({
                    'academic_year': year,
                    'required_amount': annual_fee,
                    'paid_amount': paid_amount,
                    'balance': annual_fee - paid_amount,
                    'status': 'paid' if paid_amount >= annual_fee else 'unpaid'
                })
                
                total_required += annual_fee
                total_paid += paid_amount
            
            return {
                'student_reg_no': student['reg_no'],
                'department': student['department_name'],
                'annual_fee': annual_fee,
                'payment_details': payment_summary,
                'total_required': total_required,
                'total_paid': total_paid,
                'total_balance': total_required - total_paid,
                'can_approve': total_paid >= total_required
            }
            
    except Exception as e:
        print(f"Error getting payment summary: {e}")
        return None

def validate_student_payments(request_id):
    """Validate if student has paid required fees for transcript request"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Get request details
            cursor.execute("""
                SELECT tr.*, u.id as user_id
                FROM transcript_requests tr
                JOIN users u ON tr.student_id = u.id
                WHERE tr.id = %s
            """, (request_id,))
            
            request = cursor.fetchone()
            if not request:
                return {'valid': False, 'reason': 'Request not found'}
            
            academic_years = json.loads(request['academic_years'])
            payment_summary = get_student_payment_summary(request['user_id'], academic_years)
            
            if not payment_summary:
                return {'valid': False, 'reason': 'Unable to verify payments'}
            
            if payment_summary['can_approve']:
                return {
                    'valid': True, 
                    'payment_summary': payment_summary,
                    'message': 'All required fees have been paid'
                }
            else:
                return {
                    'valid': False,
                    'payment_summary': payment_summary,
                    'reason': f'Outstanding balance: {payment_summary["total_balance"]:,.0f} RWF'
                }
                
    except Exception as e:
        print(f"Error validating payments: {e}")
        return {'valid': False, 'reason': 'Payment validation error'}

def get_student_available_academic_years(student_reg_no):
    """Get academic years available for transcript request based on fee payments"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Get student info
            cursor.execute("""
                SELECT id, enrollment_start_year FROM users 
                WHERE reg_no = %s AND role = 'student'
            """, (student_reg_no,))
            
            student = cursor.fetchone()
            if not student:
                return []
            
            # Get years with fee payments (completed years only)
            cursor.execute("""
                SELECT DISTINCT academic_year FROM student_payments 
                WHERE student_id = %s AND amount_paid > 0
                ORDER BY academic_year
            """, (student['id'],))
            
            paid_years = [row['academic_year'] for row in cursor.fetchall()]
            
            # Current academic year (exclude this)
            current_year = 2025
            current_academic_year = f"{current_year}-{current_year + 1}"
            
            # Only return years with payments and exclude current year
            available_years = []
            for year_str in paid_years:
                if year_str != current_academic_year:
                    available_years.append(year_str)
            
            return available_years
            
    except Exception as e:
        print(f"Error getting available years: {e}")
        return []

# Compatibility functions
def add_payment(student_reg_no, academic_year, amount, payment_method, request_id, department):
    return True