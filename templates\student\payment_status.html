{% extends "student/base.html" %}

{% block title %}Payment Status{% endblock %}

{% block content %}
<div class="container">
    <!-- Header Section with Steps -->
    <div class="payment-status-header">
        <div class="header-content">
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Request Details</div>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <div class="step-label">Payment Method</div>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-label">Payment Status</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Completion</div>
                </div>
            </div>
            
            <h1>Payment Status</h1>
            <p class="subtitle">Checking your Momo payment transaction status</p>
        </div>
    </div>

    <!-- Payment Status Card -->
    <div class="payment-status-card">
        <div class="status-content">
            <!-- Loading Animation -->
            <div class="loading-animation" id="loadingAnimation">
                <div class="spinner"></div>
            </div>
            
            <!-- Processing State -->
            <div class="status-message" id="processingStatus">
                <h2>Processing Payment...</h2>
                <div class="phone-icon">
                    📱 Check your phone now!
                </div>
                <p class="instruction">
                    You should receive an SMS asking you to enter your Mobile Money PIN.
                </p>
                <p class="pin-instruction">
                    Enter your PIN to complete the payment.
                </p>
                
                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                
                <p class="waiting-text">Waiting for you to enter your PIN on your phone...</p>
            </div>
            
            <!-- Success State -->
            <div class="status-message success-message" id="successStatus" style="display: none;">
                <div class="success-icon">✅</div>
                <h2>Payment Successful!</h2>
                <p>Your payment has been processed successfully.</p>
                <p>Your transcript request is being submitted...</p>
            </div>
            
            <!-- Error State -->
            <div class="status-message error-message" id="errorStatus" style="display: none;">
                <div class="error-icon">❌</div>
                <h2>Payment Failed</h2>
                <p id="errorMessage">Payment was not completed. Please try again.</p>
                <button class="retry-btn" onclick="retryPayment()">
                    <i class="fas fa-redo"></i> Retry Payment
                </button>
            </div>
        </div>
        
        <!-- Payment Details -->
        <div class="payment-details">
            <h3>Payment Details</h3>
            <div class="detail-item">
                <span class="label">Amount:</span>
                <span class="value">100 RWF (Test Amount)</span>
            </div>
            <div class="detail-item">
                <span class="label">Phone Number:</span>
                <span class="value" id="phoneNumber">{{ phone_number }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Transaction ID:</span>
                <span class="value" id="transactionId">{{ transaction_id }}</span>
            </div>
        </div>
    </div>
</div>

<style>
.payment-status-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 0;
    text-align: center;
    margin-bottom: 30px;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.step.completed .step-number {
    background: #4CAF50;
}

.step.active .step-number {
    background: #FF9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.5);
}

.step-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.payment-status-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.payment-status-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.loading-animation {
    margin-bottom: 30px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-message h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.phone-icon {
    font-size: 2rem;
    margin: 20px 0;
}

.instruction {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 10px;
}

.pin-instruction {
    font-size: 1rem;
    color: #333;
    font-weight: 600;
    margin-bottom: 30px;
}

.progress-container {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    margin: 20px 0;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    width: 0%;
    animation: progress 30s linear infinite;
}

@keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
}

.waiting-text {
    font-style: italic;
    color: #888;
    margin-top: 20px;
}

.success-message {
    color: #4CAF50;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.error-message {
    color: #f44336;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.retry-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 20px;
    transition: background 0.3s;
}

.retry-btn:hover {
    background: #d32f2f;
}

.payment-details {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #eee;
    text-align: left;
}

.payment-details h3 {
    margin-bottom: 20px;
    color: #333;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
}

.detail-item .label {
    font-weight: 600;
    color: #666;
}

.detail-item .value {
    color: #333;
    font-weight: 500;
}
</style>

<script>
let paymentCheckInterval;
let transactionId = "{{ transaction_id }}";

// Start payment status checking
document.addEventListener('DOMContentLoaded', function() {
    startPaymentStatusCheck();
});

function startPaymentStatusCheck() {
    // Check payment status every 3 seconds
    paymentCheckInterval = setInterval(checkPaymentStatus, 3000);
    
    // Stop checking after 5 minutes (timeout)
    setTimeout(function() {
        if (paymentCheckInterval) {
            clearInterval(paymentCheckInterval);
            showError("Payment timeout. Please try again.");
        }
    }, 300000); // 5 minutes
}

function checkPaymentStatus() {
    fetch('/api/check-payment-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_id: transactionId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess();
            clearInterval(paymentCheckInterval);
            
            // Redirect to completion page after 3 seconds
            setTimeout(function() {
                window.location.href = '/student/payment-complete';
            }, 3000);
        } else if (data.status === 'failed') {
            showError(data.message || 'Payment failed');
            clearInterval(paymentCheckInterval);
        }
        // If status is 'pending', continue checking
    })
    .catch(error => {
        console.error('Error checking payment status:', error);
    });
}

function showSuccess() {
    document.getElementById('processingStatus').style.display = 'none';
    document.getElementById('successStatus').style.display = 'block';
}

function showError(message) {
    document.getElementById('processingStatus').style.display = 'none';
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorStatus').style.display = 'block';
}

function retryPayment() {
    // Redirect back to payment method selection
    window.location.href = '/student/payment';
}
</script>
{% endblock %}
