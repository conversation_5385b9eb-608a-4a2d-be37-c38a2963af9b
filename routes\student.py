"""
Student routes for INES Transcript System
Student dashboard, requests, and downloads
"""
from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify
from utils.decorators import login_required, student_required
from services.student_service import (
    get_student_dashboard_data,
    create_transcript_request,
    get_student_requests,
    download_transcript_file
)

student_bp = Blueprint('student', __name__)

@student_bp.route('/dashboard')
@login_required
@student_required
def dashboard():
    """Student dashboard"""
    student_id = session['user_id']
    dashboard_data = get_student_dashboard_data(student_id)
    
    return render_template('student/dashboard.html', **dashboard_data)

@student_bp.route('/request-transcript', methods=['GET', 'POST'])
@login_required
@student_required
def request_transcript():
    """Request transcript form and submission"""
    if request.method == 'POST':
        academic_years = request.form.getlist('academic_years')
        email = request.form.get('email')
        
        if not academic_years:
            flash('Please select at least one academic year', 'error')
            return redirect(url_for('student.request_transcript'))
        
        # Store in session for payment
        session['transcript_request'] = {
            'academic_years': academic_years,
            'email': email,
            'count': len(academic_years),
            'total_price': len(academic_years) * 1000  # Price per transcript
        }
        
        return redirect(url_for('student.request_summary'))
    
    # GET request - show form
    from simple_database_service import get_student_available_academic_years
    available_years = get_student_available_academic_years(session['user_id'])
    
    return render_template('student/request_transcript.html', 
                         available_years=available_years)

@student_bp.route('/request-summary')
@login_required
@student_required
def request_summary():
    """Request summary page"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))
    
    return render_template('student/request_summary.html', 
                         request=session['transcript_request'])

@student_bp.route('/payment', methods=['GET', 'POST'])
@login_required
@student_required
def payment():
    """Payment method selection"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))
    
    if request.method == 'POST':
        payment_method = request.form.get('payment_method')
        session['transcript_request']['payment_method'] = payment_method
        return redirect(url_for('student.payment_proof'))
    
    return render_template('student/payment.html', 
                         request=session['transcript_request'])

@student_bp.route('/payment-proof', methods=['GET', 'POST'])
@login_required
@student_required
def payment_proof():
    """Payment proof upload"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))
    
    if request.method == 'POST':
        if 'payment_proof' not in request.files:
            flash('Please upload payment proof', 'error')
            return redirect(url_for('student.payment_proof'))
        
        file = request.files['payment_proof']
        if file.filename == '':
            flash('Please select a file', 'error')
            return redirect(url_for('student.payment_proof'))
        
        # Create request
        request_data = session.pop('transcript_request')
        success = create_transcript_request(session['user_id'], request_data, file)
        
        if success:
            flash('Request submitted successfully!', 'success')
        else:
            flash('Error submitting request', 'error')
        
        return redirect(url_for('student.dashboard'))
    
    return render_template('student/payment_proof.html')

@student_bp.route('/request-status')
@login_required
@student_required
def request_status():
    """View request status"""
    requests = get_student_requests(session['user_id'])
    return render_template('student/request_status.html', requests=requests)

@student_bp.route('/view-downloads')
@login_required
@student_required
def view_downloads():
    """View completed downloads"""
    requests = get_student_requests(session['user_id'], status='completed')
    return render_template('student/view_downloads.html', 
                         approved_requests=requests)

@student_bp.route('/download/<int:request_id>')
@login_required
@student_required
def download_transcript(request_id):
    """Download transcript file"""
    return download_transcript_file(session['user_id'], request_id)

@student_bp.route('/api/dashboard-stats')
@login_required
@student_required
def dashboard_stats():
    """API endpoint for dashboard statistics"""
    student_id = session['user_id']
    stats = get_student_dashboard_data(student_id, stats_only=True)
    return jsonify(stats)