{% extends "student/base.html" %}

{% block title %}Mobile Money Payment{% endblock %}

{% block content %}
<div class="container">
    <!-- Header Section with Steps -->
    <div class="payment-header">
        <div class="header-content">
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Request Details</div>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <div class="step-label">Payment Method</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">Payment Status</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Completion</div>
                </div>
            </div>
            
            <h1>Mobile Money Payment</h1>
            <p class="subtitle">Enter your phone number to complete the payment</p>
        </div>
    </div>

    <!-- Payment Summary Card -->
    <div class="payment-summary-card">
        <h3><i class="fas fa-receipt"></i> Payment Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="label">Academic Years:</span>
                <span class="value">{{ request.academic_years|join(', ') }}</span>
            </div>
            <div class="summary-item">
                <span class="label">Number of Transcripts:</span>
                <span class="value">{{ request.count }}</span>
            </div>
            <div class="summary-item">
                <span class="label">Total Amount:</span>
                <span class="value amount">{{ "{:,.0f}".format(request.total_price) }} RWF</span>
            </div>
        </div>
    </div>

    <!-- Payment Form -->
    <div class="payment-form-card">
        <form method="POST" id="momoPaymentForm">
            <div class="form-group">
                <label for="phone_number">
                    <i class="fas fa-mobile-alt"></i> Mobile Money Phone Number
                </label>
                <div class="phone-input-container">
                    <span class="country-code">+250</span>
                    <input type="tel" 
                           id="phone_number" 
                           name="phone_number" 
                           placeholder="788123456"
                           pattern="[0-9]{9}"
                           maxlength="9"
                           required>
                </div>
                <div class="input-help">
                    <i class="fas fa-info-circle"></i>
                    Enter your 9-digit phone number (without +250 or 0)
                </div>
            </div>

            <div class="payment-info">
                <div class="info-item">
                    <i class="fas fa-shield-alt"></i>
                    <div>
                        <strong>Secure Payment</strong>
                        <p>Your payment is processed securely through MTN Mobile Money</p>
                    </div>
                </div>
                <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <div>
                        <strong>Quick Process</strong>
                        <p>You'll receive an SMS to complete the payment on your phone</p>
                    </div>
                </div>
                <div class="info-item">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>Auto Submission</strong>
                        <p>Your request will be submitted automatically after payment</p>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary btn-block" id="payButton">
                    <i class="fas fa-credit-card"></i>
                    Pay {{ "{:,.0f}".format(request.total_price) }} RWF
                </button>
                
                <a href="{{ url_for('student.payment') }}" class="btn btn-secondary btn-block">
                    <i class="fas fa-arrow-left"></i> Back to Payment Methods
                </a>
            </div>
        </form>
    </div>
</div>

<style>
.payment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 0;
    text-align: center;
    margin-bottom: 30px;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.step.completed .step-number {
    background: #4CAF50;
}

.step.active .step-number {
    background: #FF9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.5);
}

.step-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.payment-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.payment-summary-card, .payment-form-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    border-left: 5px solid #083464;
}

.payment-summary-card h3 {
    color: #083464;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.summary-grid {
    display: grid;
    gap: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1rem;
}

.summary-item .label {
    color: #666;
    font-weight: 500;
}

.summary-item .value {
    color: #333;
    font-weight: 600;
}

.summary-item .amount {
    color: #083464;
    font-size: 1.2rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.phone-input-container {
    display: flex;
    border: 2px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: border-color 0.3s;
}

.phone-input-container:focus-within {
    border-color: #083464;
}

.country-code {
    background: #f8f9fa;
    padding: 12px 15px;
    border-right: 1px solid #ddd;
    font-weight: 600;
    color: #666;
    display: flex;
    align-items: center;
}

.phone-input-container input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
    font-size: 1rem;
}

.input-help {
    margin-top: 8px;
    font-size: 0.9rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

.payment-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 25px 0;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item i {
    color: #083464;
    font-size: 1.2rem;
    margin-top: 2px;
}

.info-item strong {
    color: #333;
    display: block;
    margin-bottom: 5px;
}

.info-item p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

.form-actions {
    display: grid;
    gap: 15px;
}

.btn {
    padding: 15px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: #083464;
    color: white;
}

.btn-primary:hover {
    background: #0a4078;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-block {
    width: 100%;
}

@media (max-width: 768px) {
    .step-indicator {
        gap: 10px;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .step-label {
        font-size: 0.8rem;
    }
    
    .payment-header h1 {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('momoPaymentForm');
    const phoneInput = document.getElementById('phone_number');
    const payButton = document.getElementById('payButton');
    
    // Format phone number input
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
        
        // Limit to 9 digits
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        
        e.target.value = value;
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        const phoneNumber = phoneInput.value;
        
        // Validate phone number
        if (phoneNumber.length !== 9) {
            e.preventDefault();
            alert('Please enter a valid 9-digit phone number');
            return;
        }
        
        // Prevent double submission
        payButton.disabled = true;
        payButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        
        // Re-enable button after 5 seconds in case of error
        setTimeout(function() {
            payButton.disabled = false;
            payButton.innerHTML = '<i class="fas fa-credit-card"></i> Pay {{ "{:,.0f}".format(request.total_price) }} RWF';
        }, 5000);
    });
});
</script>
{% endblock %}
